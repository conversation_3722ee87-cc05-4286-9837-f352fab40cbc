#Requires -Version 5.1

<#
.SYNOPSIS
    One-click setup for the blur detection API load testing project
.DESCRIPTION
    This script sets up the complete environment for load testing blur detection APIs
.PARAMETER SkipK6Install
    Skip k6 installation if already installed
.PARAMETER InstallMethod
    k6 installation method: 'chocolatey', 'direct', 'winget', or 'auto'
.EXAMPLE
    .\setup.ps1
.EXAMPLE
    .\setup.ps1 -SkipK6Install -InstallMethod chocolatey
#>

param(
    [switch]$SkipK6Install,
    [ValidateSet('chocolatey', 'direct', 'winget', 'auto')]
    [string]$InstallMethod = 'auto'
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Script information
$ScriptVersion = "1.0.0"
$ProjectName = "Blur Detection API Load Testing"

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS', 'HEADER')]
        [string]$Level = 'INFO'
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        'INFO' { 'White' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'SUCCESS' { 'Green' }
        'HEADER' { 'Cyan' }
    }
    
    if ($Level -eq 'HEADER') {
        Write-Host ""
        Write-Host "=" * 60 -ForegroundColor $color
        Write-Host $Message -ForegroundColor $color
        Write-Host "=" * 60 -ForegroundColor $color
        Write-Host ""
    } else {
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

# Display banner
function Show-Banner {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                                                              ║" -ForegroundColor Cyan
    Write-Host "║           Blur Detection API Load Testing Setup              ║" -ForegroundColor Cyan
    Write-Host "║                        Version $ScriptVersion                        ║" -ForegroundColor Cyan
    Write-Host "║                                                              ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
}

# Check system requirements
function Test-SystemRequirements {
    Write-Log "Checking system requirements..." -Level HEADER
    
    # Check PowerShell version
    $psVersion = $PSVersionTable.PSVersion
    Write-Log "PowerShell version: $psVersion" -Level INFO
    
    if ($psVersion.Major -lt 5) {
        Write-Log "PowerShell 5.1 or higher is required" -Level ERROR
        return $false
    }
    
    # Check Windows version
    $osVersion = [System.Environment]::OSVersion.Version
    Write-Log "Windows version: $osVersion" -Level INFO
    
    # Check available disk space
    $drive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    Write-Log "Available disk space: ${freeSpaceGB}GB" -Level INFO
    
    if ($freeSpaceGB -lt 1) {
        Write-Log "At least 1GB of free disk space is recommended" -Level WARN
    }
    
    # Check internet connectivity
    try {
        $testConnection = Test-NetConnection -ComputerName "github.com" -Port 443 -InformationLevel Quiet
        if ($testConnection) {
            Write-Log "Internet connectivity: OK" -Level SUCCESS
        } else {
            Write-Log "Internet connectivity: Failed" -Level WARN
        }
    }
    catch {
        Write-Log "Internet connectivity: Could not verify" -Level WARN
    }
    
    Write-Log "System requirements check completed" -Level SUCCESS
    return $true
}

# Validate project structure
function Test-ProjectStructure {
    Write-Log "Validating project structure..." -Level HEADER
    
    $requiredDirectories = @(
        "config",
        "scripts",
        "k6-tests"
    )
    
    $requiredFiles = @(
        "config/test-config.json",
        "config/image-urls.json",
        "scripts/install-k6.ps1",
        "scripts/run-tests.ps1",
        "scripts/analyze-results.ps1",
        "k6-tests/python-api-test.js",
        "k6-tests/golang-api-test.js",
        "k6-tests/utils.js"
    )
    
    $missingItems = @()
    
    # Check directories
    foreach ($dir in $requiredDirectories) {
        if (-not (Test-Path $dir)) {
            $missingItems += "Directory: $dir"
        } else {
            Write-Log "✓ Directory found: $dir" -Level SUCCESS
        }
    }
    
    # Check files
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingItems += "File: $file"
        } else {
            Write-Log "✓ File found: $file" -Level SUCCESS
        }
    }
    
    if ($missingItems.Count -gt 0) {
        Write-Log "Missing project components:" -Level ERROR
        foreach ($item in $missingItems) {
            Write-Log "  - $item" -Level ERROR
        }
        return $false
    }
    
    Write-Log "Project structure validation completed" -Level SUCCESS
    return $true
}

# Create output directories
function Initialize-OutputDirectories {
    Write-Log "Creating output directories..." -Level HEADER
    
    $directories = @(
        "results",
        "results/raw",
        "results/processed",
        "results/logs",
        "dashboard",
        "dashboard/assets",
        "dashboard/assets/css",
        "dashboard/assets/js",
        "dashboard/assets/charts",
        "dashboard/data"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Log "✓ Created directory: $dir" -Level SUCCESS
        } else {
            Write-Log "✓ Directory exists: $dir" -Level INFO
        }
    }
    
    Write-Log "Output directories initialized" -Level SUCCESS
}

# Install k6
function Install-K6Tool {
    Write-Log "Installing k6 load testing tool..." -Level HEADER
    
    if ($SkipK6Install) {
        Write-Log "Skipping k6 installation as requested" -Level INFO
        return $true
    }
    
    # Check if k6 is already installed
    try {
        $k6Version = & k6 version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "k6 is already installed: $($k6Version -split "`n" | Select-Object -First 1)" -Level SUCCESS
            return $true
        }
    }
    catch {
        # k6 not found, proceed with installation
    }
    
    # Run k6 installation script
    $installScript = Join-Path "scripts" "install-k6.ps1"
    if (Test-Path $installScript) {
        Write-Log "Running k6 installation script..." -Level INFO
        
        try {
            & $installScript -Method $InstallMethod
            if ($LASTEXITCODE -eq 0) {
                Write-Log "k6 installation completed successfully" -Level SUCCESS
                return $true
            } else {
                Write-Log "k6 installation failed" -Level ERROR
                return $false
            }
        }
        catch {
            Write-Log "Error running k6 installation script: $($_.Exception.Message)" -Level ERROR
            return $false
        }
    } else {
        Write-Log "k6 installation script not found: $installScript" -Level ERROR
        return $false
    }
}

# Validate configuration files
function Test-ConfigurationFiles {
    Write-Log "Validating configuration files..." -Level HEADER
    
    # Validate test-config.json
    $testConfigPath = "config/test-config.json"
    try {
        $testConfig = Get-Content $testConfigPath -Raw | ConvertFrom-Json
        Write-Log "✓ test-config.json is valid JSON" -Level SUCCESS
        
        # Check required sections
        $requiredSections = @('test_parameters', 'apis', 'logging', 'output', 'validation', 'dashboard')
        foreach ($section in $requiredSections) {
            if ($testConfig.PSObject.Properties.Name -contains $section) {
                Write-Log "✓ Configuration section found: $section" -Level SUCCESS
            } else {
                Write-Log "✗ Missing configuration section: $section" -Level ERROR
                return $false
            }
        }
    }
    catch {
        Write-Log "Invalid test-config.json: $($_.Exception.Message)" -Level ERROR
        return $false
    }
    
    # Validate image-urls.json
    $imageUrlsPath = "config/image-urls.json"
    try {
        $imageUrls = Get-Content $imageUrlsPath -Raw | ConvertFrom-Json
        Write-Log "✓ image-urls.json is valid JSON" -Level SUCCESS
        
        if ($imageUrls.image_urls -and $imageUrls.image_urls.Count -gt 0) {
            Write-Log "✓ Found $($imageUrls.image_urls.Count) image URLs" -Level SUCCESS
        } else {
            Write-Log "✗ No image URLs found in configuration" -Level ERROR
            return $false
        }
    }
    catch {
        Write-Log "Invalid image-urls.json: $($_.Exception.Message)" -Level ERROR
        return $false
    }
    
    Write-Log "Configuration files validation completed" -Level SUCCESS
    return $true
}

# Perform connectivity tests
function Test-APIConnectivity {
    Write-Log "Testing API connectivity..." -Level HEADER
    
    try {
        $config = Get-Content "config/test-config.json" -Raw | ConvertFrom-Json
        
        # Test Python API
        $pythonEndpoint = $config.apis.python.endpoint
        Write-Log "Testing Python API: $pythonEndpoint" -Level INFO
        
        try {
            $response = Invoke-WebRequest -Uri $pythonEndpoint -Method HEAD -TimeoutSec 10 -UseBasicParsing
            Write-Log "✓ Python API is reachable (Status: $($response.StatusCode))" -Level SUCCESS
        }
        catch {
            Write-Log "⚠ Python API connectivity test failed: $($_.Exception.Message)" -Level WARN
        }
        
        # Test Golang API
        $golangEndpoint = $config.apis.golang.endpoint
        Write-Log "Testing Golang API: $golangEndpoint" -Level INFO
        
        try {
            $response = Invoke-WebRequest -Uri $golangEndpoint -Method HEAD -TimeoutSec 10 -UseBasicParsing
            Write-Log "✓ Golang API is reachable (Status: $($response.StatusCode))" -Level SUCCESS
        }
        catch {
            Write-Log "⚠ Golang API connectivity test failed: $($_.Exception.Message)" -Level WARN
        }
    }
    catch {
        Write-Log "Error during connectivity tests: $($_.Exception.Message)" -Level WARN
    }
    
    Write-Log "API connectivity tests completed" -Level SUCCESS
}

# Run comprehensive validation
function Invoke-ConfigValidation {
    Write-Log "Running comprehensive configuration validation..." -Level INFO

    $validationScript = Join-Path "scripts" "validate-config.ps1"
    if (Test-Path $validationScript) {
        try {
            & $validationScript -Strict
            if ($LASTEXITCODE -eq 0) {
                Write-Log "Configuration validation passed" -Level SUCCESS
                return $true
            } else {
                Write-Log "Configuration validation failed" -Level ERROR
                return $false
            }
        }
        catch {
            Write-Log "Error running configuration validation: $($_.Exception.Message)" -Level ERROR
            return $false
        }
    } else {
        Write-Log "Configuration validation script not found: $validationScript" -Level WARN
        return $true
    }
}

# Display setup summary
function Show-SetupSummary {
    Write-Log "Setup Summary" -Level HEADER
    
    Write-Log "Project setup completed successfully!" -Level SUCCESS
    Write-Log "" -Level INFO
    Write-Log "Available commands:" -Level INFO
    Write-Log "  .\scripts\run-tests.ps1          - Run load tests" -Level INFO
    Write-Log "  .\scripts\analyze-results.ps1    - Analyze test results" -Level INFO
    Write-Log "  .\scripts\generate-dashboard.ps1 - Generate dashboards" -Level INFO
    Write-Log "" -Level INFO
    Write-Log "Quick start:" -Level INFO
    Write-Log "  1. Review configuration files in the 'config' directory" -Level INFO
    Write-Log "  2. Run: .\scripts\run-tests.ps1" -Level INFO
    Write-Log "  3. Run: .\scripts\analyze-results.ps1" -Level INFO
    Write-Log "  4. Run: .\scripts\generate-dashboard.ps1" -Level INFO
    Write-Log "  5. Open dashboard\index.html in your browser" -Level INFO
    Write-Log "" -Level INFO
    Write-Log "For detailed instructions, see README.md" -Level INFO
}

# Main setup function
function Start-Setup {
    Show-Banner
    
    Write-Log "Starting $ProjectName setup..." -Level INFO
    Write-Log "Setup method: $InstallMethod" -Level INFO
    Write-Log "Skip k6 install: $SkipK6Install" -Level INFO
    
    # Run setup steps
    $steps = @(
        { Test-SystemRequirements },
        { Test-ProjectStructure },
        { Initialize-OutputDirectories },
        { Install-K6Tool },
        { Test-ConfigurationFiles },
        { Test-APIConnectivity },
        { Invoke-ConfigValidation }
    )

    $stepNames = @(
        "System Requirements Check",
        "Project Structure Validation",
        "Output Directories Initialization",
        "k6 Installation",
        "Configuration Files Validation",
        "API Connectivity Tests",
        "Comprehensive Configuration Validation"
    )
    
    for ($i = 0; $i -lt $steps.Count; $i++) {
        Write-Log "Step $($i + 1)/$($steps.Count): $($stepNames[$i])" -Level INFO
        
        $result = & $steps[$i]
        if (-not $result) {
            Write-Log "Setup failed at step: $($stepNames[$i])" -Level ERROR
            exit 1
        }
    }
    
    Show-SetupSummary
}

# Execute main setup
try {
    Start-Setup
}
catch {
    Write-Log "Unexpected error during setup: $($_.Exception.Message)" -Level ERROR
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level ERROR
    exit 1
}
