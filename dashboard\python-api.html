<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python API - Detailed Performance Analysis</title>
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🐍</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>🐍 Python API Performance Analysis</h1>
            <p class="subtitle">Detailed Load Testing Results</p>
            <div style="margin-top: 15px;">
                <small>Endpoint: <code>https://blur.lemoncoast-53375c73.centralindia.azurecontainerapps.io/detect-blur</code></small><br>
                <small>Test Run: <span id="test-timestamp">Loading...</span></small><br>
                <small>Generated: <span id="generated-at">Loading...</span></small>
            </div>
        </div>

        <!-- Key Metrics Cards -->
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>📊 Request Statistics</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <div><strong>Total Requests:</strong> <span id="total-requests">-</span></div>
                    <div><strong>Successful:</strong> <span id="successful-requests">-</span></div>
                    <div><strong>Failed:</strong> <span id="failed-requests">-</span></div>
                    <div><strong>Success Rate:</strong> <span id="success-rate">-</span></div>
                </div>
            </div>

            <div class="metric-card">
                <h3>⚡ Response Time Metrics</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <div><strong>Average:</strong> <span id="avg-response">-</span></div>
                    <div><strong>Median:</strong> <span id="median-response">-</span></div>
                    <div><strong>P95:</strong> <span id="p95-response">-</span></div>
                    <div><strong>P99:</strong> <span id="p99-response">-</span></div>
                </div>
            </div>

            <div class="metric-card">
                <h3>🚀 Throughput Metrics</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <div><strong>Requests/sec:</strong> <span id="requests-per-sec">-</span></div>
                    <div><strong>Successful/sec:</strong> <span id="successful-per-sec">-</span></div>
                    <div><strong>Min Response:</strong> <span id="min-response">-</span></div>
                    <div><strong>Max Response:</strong> <span id="max-response">-</span></div>
                </div>
            </div>

            <div class="metric-card">
                <h3>⚠️ Error Analysis</h3>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                    <div><strong>Error Rate:</strong> <span id="error-rate">-</span></div>
                    <div><strong>P90 Response:</strong> <span id="p90-response">-</span></div>
                    <div><strong>Test Duration:</strong> <span id="test-duration">-</span></div>
                    <div><strong>Status:</strong> <span id="api-status">-</span></div>
                </div>
            </div>
        </div>

        <!-- Response Time Distribution Chart -->
        <div class="chart-container">
            <h3>📈 Response Time Distribution</h3>
            <div class="chart-wrapper">
                <canvas id="responseDistributionChart"></canvas>
            </div>
            <button class="export-btn" onclick="exportChart('responseDistribution')">📥 Export Chart</button>
        </div>

        <!-- Performance Over Time -->
        <div class="chart-container">
            <h3>📊 Performance Percentiles</h3>
            <div class="chart-wrapper">
                <canvas id="percentilesChart"></canvas>
            </div>
            <button class="export-btn" onclick="exportChart('percentiles')">📥 Export Chart</button>
        </div>

        <!-- Status Code Distribution -->
        <div class="chart-container">
            <h3>🔍 Response Status Analysis</h3>
            <div class="chart-wrapper">
                <canvas id="statusChart"></canvas>
            </div>
            <p style="text-align: center; margin-top: 15px; color: #7f8c8d;">
                <small>Status code distribution shows the reliability of the API responses</small>
            </p>
            <button class="export-btn" onclick="exportChart('status')">📥 Export Chart</button>
        </div>

        <!-- Performance Recommendations -->
        <div class="chart-container">
            <h3>💡 Performance Recommendations</h3>
            <div id="recommendations-content">
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="chart-container">
            <h3>🔗 Navigation</h3>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <a href="index.html" class="export-btn" style="text-decoration: none;">🏠 Main Dashboard</a>
                <a href="golang-api.html" class="export-btn" style="text-decoration: none;">🔷 Golang API</a>
                <button class="export-btn" onclick="window.print()">🖨️ Print Report</button>
                <button class="export-btn" onclick="location.reload()">🔄 Refresh Data</button>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>Python API Performance Analysis | Generated by Blur Detection API Load Testing Suite</p>
    </div>

    <script>
        let dashboardData = null;
        let charts = {};

        // Load dashboard data
        async function loadData() {
            try {
                const response = await fetch('./data/dashboard-data.json');
                dashboardData = await response.json();
                console.log('Dashboard data loaded:', dashboardData);
                updateDashboard();
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showError('Failed to load dashboard data');
            }
        }

        function showError(message) {
            const container = document.querySelector('.container');
            container.innerHTML = `
                <div class="error-message" style="background: #e74c3c; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <h2>Error</h2>
                    <p>${message}</p>
                </div>
            `;
        }

        function updateDashboard() {
            if (!dashboardData) return;

            const python = dashboardData.python;

            // Update metadata
            document.getElementById('test-timestamp').textContent = dashboardData.metadata.test_timestamp;
            document.getElementById('generated-at').textContent = dashboardData.metadata.generated_at;

            // Update metrics
            document.getElementById('total-requests').textContent = python.TotalRequests.toLocaleString();
            document.getElementById('successful-requests').textContent = python.SuccessfulRequests.toLocaleString();
            document.getElementById('failed-requests').textContent = python.FailedRequests.toLocaleString();
            document.getElementById('success-rate').textContent = python.SuccessRate + '%';
            document.getElementById('avg-response').textContent = python.AvgResponseTime + ' ms';
            document.getElementById('median-response').textContent = python.MedianResponseTime + ' ms';
            document.getElementById('p95-response').textContent = python.P95ResponseTime + ' ms';
            document.getElementById('p99-response').textContent = python.P99ResponseTime + ' ms';
            document.getElementById('requests-per-sec').textContent = python.RequestsPerSecond;
            document.getElementById('successful-per-sec').textContent = python.SuccessfulRequestsPerSecond;
            document.getElementById('min-response').textContent = python.MinResponseTime + ' ms';
            document.getElementById('max-response').textContent = python.MaxResponseTime + ' ms';
            document.getElementById('error-rate').textContent = python.ErrorRate + '%';
            document.getElementById('p90-response').textContent = python.P90ResponseTime + ' ms';
            document.getElementById('test-duration').textContent = Math.round(python.TestDurationSeconds) + ' sec';
            document.getElementById('api-status').textContent = python.SuccessRate > 95 ? '✅ Excellent' : python.SuccessRate > 90 ? '✅ Good' : python.SuccessRate > 80 ? '⚠️ Fair' : '❌ Poor';

            // Create charts
            createResponseDistributionChart();
            createPercentilesChart();
            createStatusChart();
            generateRecommendations();
        }

        function createResponseDistributionChart() {
            const ctx = document.getElementById('responseDistributionChart').getContext('2d');
            const python = dashboardData.python;
            
            charts.responseDistribution = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Min', 'Average', 'Median', 'P90', 'P95', 'P99', 'Max'],
                    datasets: [{
                        label: 'Response Time (ms)',
                        data: [
                            python.MinResponseTime,
                            python.AvgResponseTime,
                            python.MedianResponseTime,
                            python.P90ResponseTime,
                            python.P95ResponseTime,
                            python.P99ResponseTime,
                            python.MaxResponseTime
                        ],
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Response Time (ms)'
                            }
                        }
                    }
                }
            });
        }

        function createPercentilesChart() {
            const ctx = document.getElementById('percentilesChart').getContext('2d');
            const python = dashboardData.python;
            
            charts.percentiles = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['P50 (Median)', 'P90', 'P95', 'P99'],
                    datasets: [{
                        label: 'Response Time (ms)',
                        data: [
                            python.MedianResponseTime,
                            python.P90ResponseTime,
                            python.P95ResponseTime,
                            python.P99ResponseTime
                        ],
                        borderColor: 'rgba(52, 152, 219, 1)',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Response Time (ms)'
                            }
                        }
                    }
                }
            });
        }

        function createStatusChart() {
            const ctx = document.getElementById('statusChart').getContext('2d');
            const python = dashboardData.python;
            
            charts.status = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Successful Requests', 'Failed Requests'],
                    datasets: [{
                        data: [python.SuccessfulRequests, python.FailedRequests],
                        backgroundColor: [
                            'rgba(46, 204, 113, 0.8)',
                            'rgba(231, 76, 60, 0.8)'
                        ],
                        borderColor: [
                            'rgba(46, 204, 113, 1)',
                            'rgba(231, 76, 60, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function generateRecommendations() {
            const python = dashboardData.python;
            const recommendations = [];

            // Performance recommendations
            if (python.AvgResponseTime > 5000) {
                recommendations.push('🐌 Average response time is high (>5s). Consider optimizing image processing algorithms or scaling infrastructure.');
            } else if (python.AvgResponseTime > 2000) {
                recommendations.push('⚠️ Average response time is moderate (>2s). Monitor for potential bottlenecks.');
            } else {
                recommendations.push('✅ Average response time is good (<2s). API is performing well.');
            }

            // Success rate recommendations
            if (python.SuccessRate < 95) {
                recommendations.push('❌ Success rate is below 95%. Investigate error causes and implement retry mechanisms.');
            } else if (python.SuccessRate < 99) {
                recommendations.push('⚠️ Success rate is good but could be improved. Monitor error patterns.');
            } else {
                recommendations.push('✅ Excellent success rate (>99%). API is highly reliable.');
            }

            // P99 recommendations
            if (python.P99ResponseTime > python.AvgResponseTime * 3) {
                recommendations.push('📊 P99 response time is significantly higher than average. Some requests are experiencing delays.');
            }

            // Throughput recommendations
            if (python.RequestsPerSecond < 10) {
                recommendations.push('🚀 Low throughput detected. Consider horizontal scaling or performance optimization.');
            } else if (python.RequestsPerSecond > 100) {
                recommendations.push('🏆 High throughput achieved. API is handling load efficiently.');
            }

            // Display recommendations
            const container = document.getElementById('recommendations-content');
            container.innerHTML = recommendations.map(rec => 
                `<p style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #3498db;">${rec}</p>`
            ).join('');
        }

        function exportChart(chartName) {
            const chart = charts[chartName];
            if (chart) {
                const url = chart.toBase64Image();
                const link = document.createElement('a');
                link.download = `python-api-${chartName}-chart.png`;
                link.href = url;
                link.click();
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', loadData);
    </script>
</body>
</html>
