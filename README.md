# 🔍 Blur Detection API Load Testing Suite

A comprehensive local load testing project to compare two blur detection APIs using k6, with interactive dashboards and detailed performance analysis.

## 📋 Table of Contents

- [Overview](#overview)
- [APIs Under Test](#apis-under-test)
- [Features](#features)
- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Project Structure](#project-structure)
- [Configuration](#configuration)
- [Usage Guide](#usage-guide)
- [Dashboard Features](#dashboard-features)
- [Performance Metrics](#performance-metrics)
- [Troubleshooting](#troubleshooting)
- [Advanced Usage](#advanced-usage)
- [Contributing](#contributing)

## 🎯 Overview

This project provides a complete solution for load testing and comparing two blur detection APIs. It includes:

- **Automated k6 installation** with multiple methods (Chocolatey, direct download, Winget)
- **Parallel load testing** of both APIs with 100 concurrent users each
- **Comprehensive metrics collection** including response times, success rates, and throughput
- **Interactive HTML dashboards** with Chart.js visualizations
- **Detailed performance analysis** with recommendations
- **One-click setup** and execution via PowerShell scripts

## 🔗 APIs Under Test

### Python API
- **Endpoint**: `https://blur.lemoncoast-53375c73.centralindia.azurecontainerapps.io/detect-blur`
- **Method**: POST
- **Headers**: `Content-Type: application/json`
- **Request Format**: `{"image_url": "IMAGE_URL_HERE"}`

### Golang API
- **Endpoint**: `http://fai-imagedescribe.fieldassist.io:8080/analyze`
- **Method**: POST
- **Headers**: `Content-Type: application/json`
- **Request Format**: `{"url": "IMAGE_URL_HERE", "is_ocr": false}`

## ✨ Features

### 🚀 Load Testing
- **Concurrent Testing**: 100 virtual users per API (200 total)
- **Configurable Duration**: Default 5 minutes with 30-second ramp-up
- **Retry Logic**: Exponential backoff with max 3 retries
- **Error Handling**: Comprehensive error tracking and categorization
- **Real-time Monitoring**: Progress indicators and live status updates

### 📊 Performance Analysis
- **Response Time Metrics**: Average, median, P90, P95, P99 percentiles
- **Reliability Metrics**: Success rate, error rate, timeout analysis
- **Throughput Metrics**: Requests per second, peak throughput
- **Comparative Analysis**: Side-by-side API comparison with winner determination

### 🎨 Interactive Dashboards
- **Main Comparison Dashboard**: Side-by-side performance comparison
- **Individual API Dashboards**: Detailed analysis for each API
- **Responsive Design**: Works on desktop and mobile devices
- **Export Functionality**: Download charts as PNG images
- **Real-time Updates**: Auto-refresh capabilities

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows 10/11 or Windows Server 2016+
- **PowerShell**: Version 5.1 or higher (PowerShell Core 7+ supported)
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Disk Space**: At least 1GB free space
- **Internet Connection**: Required for k6 installation and API testing

### Network Requirements
- Access to both API endpoints
- Ability to download packages from GitHub and package repositories
- No corporate firewall blocking k6 or package manager traffic

## 🚀 Quick Start

### 1. Clone or Download the Project
```powershell
# If using Git
git clone <repository-url>
cd blur-test

# Or download and extract the ZIP file
```

### 2. Run One-Click Setup
```powershell
# Run the main setup script
.\setup.ps1

# Or with specific k6 installation method
.\setup.ps1 -InstallMethod chocolatey
```

### 3. Execute Load Tests
```powershell
# Run load tests for both APIs
.\scripts\run-tests.ps1

# Or run with custom parameters
.\scripts\run-tests.ps1 -Parallel -OutputDir ".\results"
```

### 4. Analyze Results
```powershell
# Process raw test results
.\scripts\analyze-results.ps1

# Generate interactive dashboards
.\scripts\generate-dashboard.ps1 -OpenBrowser
```

### 5. View Results
Open `dashboard\index.html` in your web browser to view the interactive performance comparison dashboard.

## 📁 Project Structure

```
blur-test/
├── README.md                    # This documentation file
├── setup.ps1                    # Main setup script (one-click setup)
├── config/
│   ├── test-config.json        # Test parameters and API configuration
│   └── image-urls.json         # List of image URLs for testing
├── scripts/
│   ├── install-k6.ps1          # k6 installation script
│   ├── run-tests.ps1           # Test execution orchestrator
│   ├── analyze-results.ps1     # Results analysis and processing
│   └── generate-dashboard.ps1  # Dashboard generation script
├── k6-tests/
│   ├── python-api-test.js      # k6 script for Python API
│   ├── golang-api-test.js      # k6 script for Golang API
│   └── utils.js                # Shared utilities and helpers
├── results/
│   ├── raw/                    # Raw k6 JSON output files
│   ├── processed/              # Analyzed CSV/JSON results
│   └── logs/                   # Test execution logs
└── dashboard/
    ├── index.html              # Main comparison dashboard
    ├── python-api.html         # Python API detailed dashboard
    ├── golang-api.html         # Golang API detailed dashboard
    ├── assets/
    │   ├── css/dashboard.css   # Dashboard styling
    │   └── js/dashboard.js     # Dashboard JavaScript
    └── data/                   # Dashboard data files
```

## ⚙️ Configuration

### Test Configuration (`config/test-config.json`)

```json
{
  "test_parameters": {
    "duration": "5m",              // Test duration
    "ramp_up_time": "30s",         // Ramp-up time
    "virtual_users_per_api": 100,  // VUs per API
    "request_timeout": "30s",      // Request timeout
    "think_time": "1s",            // Delay between requests
    "max_retries": 3               // Maximum retry attempts
  }
}
```

### Image URLs (`config/image-urls.json`)

The project includes 20 sample image URLs with various characteristics:
- **Sharp images**: Clear, non-blurred images
- **Blurred images**: Images with different blur levels
- **Various resolutions**: Small (400x300) to high (1600x1200)

You can customize the image URLs by editing this file.

## 📖 Usage Guide

### Basic Usage

1. **Setup Environment**:
   ```powershell
   .\setup.ps1
   ```

2. **Run Tests**:
   ```powershell
   .\scripts\run-tests.ps1
   ```

3. **Analyze Results**:
   ```powershell
   .\scripts\analyze-results.ps1
   .\scripts\generate-dashboard.ps1
   ```

### Advanced Usage

#### Custom Test Duration
```powershell
# Edit config/test-config.json
{
  "test_parameters": {
    "duration": "10m",  // 10 minutes instead of 5
    "virtual_users_per_api": 200  // 200 VUs instead of 100
  }
}
```

#### Sequential Testing (Non-Parallel)
```powershell
.\scripts\run-tests.ps1 -Parallel:$false
```

#### Skip Validation
```powershell
.\scripts\run-tests.ps1 -SkipValidation
```

#### Analyze Specific Test Run
```powershell
.\scripts\analyze-results.ps1 -TestRunId "20250915-143022"
```

## 🎨 Dashboard Features

### Main Comparison Dashboard (`index.html`)
- **Performance Overview**: Quick metrics comparison
- **Response Time Charts**: Bar charts comparing response time percentiles
- **Success Rate Visualization**: Doughnut charts showing reliability
- **Throughput Comparison**: Requests per second comparison
- **Detailed Metrics Table**: Complete performance breakdown
- **Performance Insights**: AI-generated recommendations

### Individual API Dashboards
- **Detailed Metrics**: Comprehensive performance statistics
- **Response Time Distribution**: Histogram of response times
- **Percentile Analysis**: P50, P90, P95, P99 response times
- **Status Code Analysis**: Success/failure breakdown
- **Performance Recommendations**: API-specific optimization suggestions

### Dashboard Features
- **Responsive Design**: Works on all screen sizes
- **Export Functionality**: Download charts as PNG images
- **Print Support**: Print-friendly layouts
- **Auto-refresh**: Real-time data updates
- **Interactive Charts**: Hover for detailed information

## 📊 Performance Metrics

### Response Time Metrics
- **Average Response Time**: Mean response time across all requests
- **Median Response Time**: 50th percentile response time
- **P90 Response Time**: 90th percentile (90% of requests faster)
- **P95 Response Time**: 95th percentile (95% of requests faster)
- **P99 Response Time**: 99th percentile (99% of requests faster)

### Reliability Metrics
- **Success Rate**: Percentage of HTTP 200 responses
- **Error Rate**: Percentage of failed requests
- **Timeout Rate**: Percentage of requests that timed out

### Throughput Metrics
- **Requests Per Second (RPS)**: Total requests processed per second
- **Successful RPS**: Successful requests processed per second
- **Peak Throughput**: Maximum RPS achieved during test

### Comparative Analysis
- **Performance Winner**: API with better performance per metric
- **Percentage Difference**: Relative performance difference
- **Overall Winner**: API with most metric wins
- **Statistical Significance**: Confidence in performance differences

## 🔧 Troubleshooting

### Common Issues

#### k6 Installation Fails
```powershell
# Try different installation method
.\scripts\install-k6.ps1 -Method direct

# Or force reinstallation
.\scripts\install-k6.ps1 -Force
```

#### API Connectivity Issues
```powershell
# Test connectivity manually
Test-NetConnection -ComputerName "blur.lemoncoast-53375c73.centralindia.azurecontainerapps.io" -Port 443

# Skip validation if needed
.\scripts\run-tests.ps1 -SkipValidation
```

#### PowerShell Execution Policy
```powershell
# Set execution policy for current user
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Or bypass for single script
PowerShell -ExecutionPolicy Bypass -File .\setup.ps1
```

#### Dashboard Not Loading
1. Check if `dashboard/data/dashboard-data.json` exists
2. Ensure you've run `analyze-results.ps1` first
3. Try opening dashboard in different browser
4. Check browser console for JavaScript errors

### Log Files

Check these log files for detailed error information:
- `results/logs/test-execution.log` - Test execution logs
- `results/logs/python-stdout.log` - Python API test output
- `results/logs/golang-stdout.log` - Golang API test output
- `results/logs/python-stderr.log` - Python API test errors
- `results/logs/golang-stderr.log` - Golang API test errors

### Performance Issues

#### High Memory Usage
- Reduce virtual users in `config/test-config.json`
- Decrease test duration
- Close other applications during testing

#### Slow Test Execution
- Check internet connection speed
- Verify API endpoints are responsive
- Consider reducing concurrent users

## 🔬 Advanced Usage

### Custom Image URLs
Edit `config/image-urls.json` to test with your own images:
```json
{
  "image_urls": [
    "https://your-domain.com/image1.jpg",
    "https://your-domain.com/image2.png"
  ]
}
```

### Environment Variables
Set environment variables for sensitive configuration:
```powershell
$env:PYTHON_API_ENDPOINT = "https://your-python-api.com/detect"
$env:GOLANG_API_ENDPOINT = "https://your-golang-api.com/analyze"
```

### Continuous Integration
Integrate with CI/CD pipelines:
```yaml
# Example GitHub Actions workflow
- name: Run Load Tests
  run: |
    .\setup.ps1 -SkipK6Install
    .\scripts\run-tests.ps1
    .\scripts\analyze-results.ps1
```

### Custom Metrics
Extend k6 tests with custom metrics:
```javascript
// In k6-tests/utils.js
export const customMetric = new Trend('custom_processing_time');
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Development Guidelines
- Follow PowerShell best practices
- Add comprehensive error handling
- Include logging for debugging
- Update documentation for new features
- Test on different Windows versions

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **k6** - Modern load testing tool
- **Chart.js** - Beautiful charts for dashboards
- **Unsplash** - Sample images for testing
- **PowerShell Community** - Scripting best practices

---

**📞 Support**: For issues or questions, please check the troubleshooting section or create an issue in the repository.

**🔄 Updates**: This project is actively maintained. Check for updates regularly.
