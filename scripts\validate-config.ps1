#Requires -Version 5.1

<#
.SYNOPSIS
    Validates configuration files and test environment
.DESCRIPTION
    This script performs comprehensive validation of configuration files, image URLs, and test environment
.PARAMETER ConfigPath
    Path to the test configuration file
.PARAMETER ImageUrlsPath
    Path to the image URLs configuration file
.PARAMETER Strict
    Enable strict validation mode with additional checks
.EXAMPLE
    .\validate-config.ps1 -ConfigPath "../config/test-config.json" -ImageUrlsPath "../config/image-urls.json"
#>

param(
    [string]$ConfigPath = "../config/test-config.json",
    [string]$ImageUrlsPath = "../config/image-urls.json",
    [switch]$Strict
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS', 'DEBUG')]
        [string]$Level = 'INFO'
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        'INFO' { 'White' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'SUCCESS' { 'Green' }
        'DEBUG' { 'Gray' }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Validation results tracking
$ValidationResults = @{
    Passed = 0
    Failed = 0
    Warnings = 0
    Issues = @()
}

# Add validation result
function Add-ValidationResult {
    param(
        [string]$Test,
        [bool]$Passed,
        [string]$Message,
        [string]$Level = 'ERROR'
    )
    
    if ($Passed) {
        $ValidationResults.Passed++
        Write-Log "✓ $Test" -Level SUCCESS
    } else {
        if ($Level -eq 'WARN') {
            $ValidationResults.Warnings++
            Write-Log "⚠ $Test - $Message" -Level WARN
        } else {
            $ValidationResults.Failed++
            Write-Log "✗ $Test - $Message" -Level ERROR
        }
        
        $ValidationResults.Issues += @{
            Test = $Test
            Message = $Message
            Level = $Level
        }
    }
}

# Validate JSON file structure
function Test-JsonFile {
    param(
        [string]$FilePath,
        [string]$Description
    )
    
    Write-Log "Validating $Description..." -Level INFO
    
    # Check if file exists
    if (-not (Test-Path $FilePath)) {
        Add-ValidationResult "$Description file exists" $false "File not found: $FilePath"
        return $null
    }
    
    Add-ValidationResult "$Description file exists" $true
    
    # Check if file is readable
    try {
        $content = Get-Content $FilePath -Raw -ErrorAction Stop
        Add-ValidationResult "$Description file is readable" $true
    }
    catch {
        Add-ValidationResult "$Description file is readable" $false $_.Exception.Message
        return $null
    }
    
    # Check if content is valid JSON
    try {
        $jsonObject = $content | ConvertFrom-Json -ErrorAction Stop
        Add-ValidationResult "$Description contains valid JSON" $true
        return $jsonObject
    }
    catch {
        Add-ValidationResult "$Description contains valid JSON" $false $_.Exception.Message
        return $null
    }
}

# Validate test configuration
function Test-TestConfiguration {
    param($Config)
    
    if (-not $Config) { return }
    
    Write-Log "Validating test configuration structure..." -Level INFO
    
    # Required sections
    $requiredSections = @('test_parameters', 'apis', 'logging', 'output', 'validation', 'dashboard')
    foreach ($section in $requiredSections) {
        $exists = $Config.PSObject.Properties.Name -contains $section
        Add-ValidationResult "Configuration section '$section' exists" $exists "Missing required section: $section"
    }
    
    # Validate test parameters
    if ($Config.test_parameters) {
        $testParams = $Config.test_parameters
        
        # Duration validation
        if ($testParams.duration) {
            $durationValid = $testParams.duration -match '^\d+[smh]$'
            Add-ValidationResult "Duration format is valid" $durationValid "Duration should be in format like '5m', '30s', '1h'"
        }
        
        # Virtual users validation
        if ($testParams.virtual_users_per_api) {
            $vuValid = $testParams.virtual_users_per_api -is [int] -and $testParams.virtual_users_per_api -gt 0 -and $testParams.virtual_users_per_api -le 1000
            Add-ValidationResult "Virtual users count is reasonable" $vuValid "Virtual users should be between 1 and 1000"
            
            if ($Strict -and $testParams.virtual_users_per_api -gt 500) {
                Add-ValidationResult "Virtual users count is not excessive" $false "High VU count may cause resource issues" "WARN"
            }
        }
        
        # Timeout validation
        if ($testParams.request_timeout) {
            $timeoutValid = $testParams.request_timeout -match '^\d+s$'
            Add-ValidationResult "Request timeout format is valid" $timeoutValid "Timeout should be in format like '30s'"
        }
        
        # Retry validation
        if ($testParams.max_retries) {
            $retryValid = $testParams.max_retries -is [int] -and $testParams.max_retries -ge 0 -and $testParams.max_retries -le 10
            Add-ValidationResult "Max retries is reasonable" $retryValid "Max retries should be between 0 and 10"
        }
    }
    
    # Validate API configurations
    if ($Config.apis) {
        foreach ($apiName in @('python', 'golang')) {
            if ($Config.apis.$apiName) {
                $api = $Config.apis.$apiName
                
                # Endpoint validation
                if ($api.endpoint) {
                    $endpointValid = $api.endpoint -match '^https?://.+'
                    Add-ValidationResult "$apiName API endpoint format is valid" $endpointValid "Endpoint should start with http:// or https://"
                }
                
                # Method validation
                if ($api.method) {
                    $methodValid = $api.method -in @('GET', 'POST', 'PUT', 'DELETE')
                    Add-ValidationResult "$apiName API method is valid" $methodValid "Method should be GET, POST, PUT, or DELETE"
                }
                
                # Headers validation
                if ($api.headers) {
                    $headersValid = $api.headers -is [PSCustomObject]
                    Add-ValidationResult "$apiName API headers format is valid" $headersValid "Headers should be an object"
                }
            } else {
                Add-ValidationResult "$apiName API configuration exists" $false "Missing API configuration for $apiName"
            }
        }
    }
}

# Validate image URLs
function Test-ImageUrls {
    param($ImageConfig)
    
    if (-not $ImageConfig) { return }
    
    Write-Log "Validating image URLs configuration..." -Level INFO
    
    # Check if image_urls array exists
    if (-not $ImageConfig.image_urls) {
        Add-ValidationResult "Image URLs array exists" $false "Missing 'image_urls' array"
        return
    }
    
    Add-ValidationResult "Image URLs array exists" $true
    
    $urls = $ImageConfig.image_urls
    $urlCount = $urls.Count
    
    # Check URL count
    Add-ValidationResult "Image URLs count is reasonable" ($urlCount -gt 0 -and $urlCount -le 1000) "Should have between 1 and 1000 URLs"
    
    if ($urlCount -lt 10) {
        Add-ValidationResult "Sufficient image URLs for testing" $false "Recommend at least 10 URLs for comprehensive testing" "WARN"
    }
    
    # Validate individual URLs
    $validUrls = 0
    $invalidUrls = @()
    
    foreach ($url in $urls) {
        if ($url -match '^https?://.+\.(jpg|jpeg|png|gif|bmp|webp)(\?.*)?$' -or $url -match '^https://images\.unsplash\.com/.+') {
            $validUrls++
        } else {
            $invalidUrls += $url
        }
    }
    
    $urlValidationPassed = $invalidUrls.Count -eq 0
    Add-ValidationResult "All image URLs have valid format" $urlValidationPassed "Invalid URLs found: $($invalidUrls.Count)"
    
    if ($invalidUrls.Count -gt 0 -and $invalidUrls.Count -le 5) {
        foreach ($invalidUrl in $invalidUrls[0..4]) {
            Write-Log "  Invalid URL: $invalidUrl" -Level DEBUG
        }
    }
    
    # Check URL diversity
    $uniqueUrls = $urls | Select-Object -Unique
    $diversityGood = $uniqueUrls.Count -eq $urls.Count
    Add-ValidationResult "Image URLs are unique" $diversityGood "Found $($urls.Count - $uniqueUrls.Count) duplicate URLs" $(if ($diversityGood) { "INFO" } else { "WARN" })
}

# Test API connectivity
function Test-APIConnectivity {
    param($Config)
    
    if (-not $Config -or -not $Config.apis) { return }
    
    Write-Log "Testing API connectivity..." -Level INFO
    
    foreach ($apiName in @('python', 'golang')) {
        if ($Config.apis.$apiName -and $Config.apis.$apiName.endpoint) {
            $endpoint = $Config.apis.$apiName.endpoint
            
            try {
                Write-Log "Testing $apiName API: $endpoint" -Level DEBUG
                $response = Invoke-WebRequest -Uri $endpoint -Method HEAD -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
                Add-ValidationResult "$apiName API is reachable" $true
            }
            catch {
                $errorMessage = $_.Exception.Message
                if ($errorMessage -like "*timeout*" -or $errorMessage -like "*timed out*") {
                    Add-ValidationResult "$apiName API is reachable" $false "Connection timeout - API may be slow or unreachable" "WARN"
                } elseif ($errorMessage -like "*404*" -or $errorMessage -like "*405*") {
                    Add-ValidationResult "$apiName API is reachable" $true "API responds (method not allowed is expected for HEAD requests)"
                } else {
                    Add-ValidationResult "$apiName API is reachable" $false $errorMessage "WARN"
                }
            }
        }
    }
}

# Test system resources
function Test-SystemResources {
    Write-Log "Checking system resources..." -Level INFO
    
    # Check available memory
    $memory = Get-WmiObject -Class Win32_OperatingSystem
    $freeMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
    $totalMemoryGB = [math]::Round($memory.TotalVisibleMemorySize / 1MB, 2)
    
    Add-ValidationResult "Sufficient memory available" ($freeMemoryGB -gt 2) "Free memory: ${freeMemoryGB}GB (recommend >2GB)"
    
    if ($Strict -and $freeMemoryGB -lt 4) {
        Add-ValidationResult "Adequate memory for high load testing" $false "Free memory: ${freeMemoryGB}GB (recommend >4GB for high load)" "WARN"
    }
    
    # Check disk space
    $drive = Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'"
    $freeSpaceGB = [math]::Round($drive.FreeSpace / 1GB, 2)
    
    Add-ValidationResult "Sufficient disk space" ($freeSpaceGB -gt 1) "Free space: ${freeSpaceGB}GB (recommend >1GB)"
    
    # Check CPU cores
    $cpu = Get-WmiObject -Class Win32_Processor
    $coreCount = $cpu.NumberOfCores
    
    Add-ValidationResult "Adequate CPU cores" ($coreCount -ge 2) "CPU cores: $coreCount (recommend ≥2)"
}

# Test k6 installation
function Test-K6Installation {
    Write-Log "Checking k6 installation..." -Level INFO
    
    try {
        $k6Version = & k6 version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Add-ValidationResult "k6 is installed and accessible" $true
            Write-Log "k6 version: $($k6Version -split "`n" | Select-Object -First 1)" -Level DEBUG
        } else {
            Add-ValidationResult "k6 is installed and accessible" $false "k6 command failed with exit code $LASTEXITCODE"
        }
    }
    catch {
        Add-ValidationResult "k6 is installed and accessible" $false "k6 not found in PATH"
    }
}

# Generate validation report
function New-ValidationReport {
    Write-Log "Validation Summary" -Level INFO
    Write-Log "=================" -Level INFO
    Write-Log "✓ Passed: $($ValidationResults.Passed)" -Level SUCCESS
    Write-Log "✗ Failed: $($ValidationResults.Failed)" -Level ERROR
    Write-Log "⚠ Warnings: $($ValidationResults.Warnings)" -Level WARN
    Write-Log "Total Tests: $($ValidationResults.Passed + $ValidationResults.Failed + $ValidationResults.Warnings)" -Level INFO
    
    if ($ValidationResults.Issues.Count -gt 0) {
        Write-Log "" -Level INFO
        Write-Log "Issues Found:" -Level INFO
        Write-Log "=============" -Level INFO
        
        foreach ($issue in $ValidationResults.Issues) {
            $icon = if ($issue.Level -eq 'WARN') { '⚠' } else { '✗' }
            Write-Log "$icon $($issue.Test): $($issue.Message)" -Level $issue.Level
        }
    }
    
    # Overall result
    $overallPassed = $ValidationResults.Failed -eq 0
    if ($overallPassed) {
        Write-Log "" -Level INFO
        Write-Log "🎉 Validation PASSED - Environment is ready for load testing!" -Level SUCCESS
        if ($ValidationResults.Warnings -gt 0) {
            Write-Log "Note: There are $($ValidationResults.Warnings) warnings that should be reviewed." -Level WARN
        }
    } else {
        Write-Log "" -Level INFO
        Write-Log "❌ Validation FAILED - Please fix the issues before running tests." -Level ERROR
    }
    
    return $overallPassed
}

# Main validation function
function Start-Validation {
    Write-Log "Starting configuration and environment validation..." -Level INFO
    Write-Log "Strict mode: $Strict" -Level INFO
    
    # Validate configuration files
    $testConfig = Test-JsonFile -FilePath $ConfigPath -Description "Test configuration"
    $imageConfig = Test-JsonFile -FilePath $ImageUrlsPath -Description "Image URLs configuration"
    
    # Validate configuration content
    Test-TestConfiguration -Config $testConfig
    Test-ImageUrls -ImageConfig $imageConfig
    
    # Test system resources
    Test-SystemResources
    
    # Test k6 installation
    Test-K6Installation
    
    # Test API connectivity (optional)
    if ($testConfig -and -not $testConfig.validation.pre_test_connectivity_check -eq $false) {
        Test-APIConnectivity -Config $testConfig
    }
    
    # Generate report
    $validationPassed = New-ValidationReport
    
    return $validationPassed
}

# Execute validation
try {
    $result = Start-Validation
    exit $(if ($result) { 0 } else { 1 })
}
catch {
    Write-Log "Unexpected error during validation: $($_.Exception.Message)" -Level ERROR
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level DEBUG
    exit 1
}
