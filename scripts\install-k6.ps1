#Requires -Version 5.1

<#
.SYNOPSIS
    Installs k6 load testing tool on Windows
.DESCRIPTION
    This script installs k6 using multiple methods:
    1. Chocolatey (preferred)
    2. Direct download from GitHub releases
    3. <PERSON><PERSON> (Windows Package Manager)
.PARAMETER Method
    Installation method: 'chocolatey', 'direct', 'winget', or 'auto'
.PARAMETER Force
    Force reinstallation even if k6 is already installed
.EXAMPLE
    .\install-k6.ps1 -Method auto
#>

param(
    [ValidateSet('chocolatey', 'direct', 'winget', 'auto')]
    [string]$Method = 'auto',
    
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS')]
        [string]$Level = 'INFO'
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        'INFO' { 'White' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'SUCCESS' { 'Green' }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Check if k6 is already installed
function Test-K6Installation {
    try {
        $version = & k6 version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "k6 is already installed: $($version -split "`n" | Select-Object -First 1)" -Level SUCCESS
            return $true
        }
    }
    catch {
        # k6 not found
    }
    return $false
}

# Install via Chocolatey
function Install-K6Chocolatey {
    Write-Log "Attempting to install k6 via Chocolatey..." -Level INFO
    
    # Check if Chocolatey is installed
    try {
        $chocoVersion = & choco --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Chocolatey not found"
        }
        Write-Log "Found Chocolatey version: $chocoVersion" -Level INFO
    }
    catch {
        Write-Log "Chocolatey is not installed. Installing Chocolatey first..." -Level WARN
        
        # Install Chocolatey
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    }
    
    # Install k6
    Write-Log "Installing k6 via Chocolatey..." -Level INFO
    & choco install k6 -y
    
    if ($LASTEXITCODE -eq 0) {
        Write-Log "k6 installed successfully via Chocolatey" -Level SUCCESS
        return $true
    } else {
        Write-Log "Failed to install k6 via Chocolatey" -Level ERROR
        return $false
    }
}

# Install via direct download
function Install-K6Direct {
    Write-Log "Attempting to install k6 via direct download..." -Level INFO
    
    try {
        # Get latest release info from GitHub API
        $releaseInfo = Invoke-RestMethod -Uri "https://api.github.com/repos/grafana/k6/releases/latest"
        $version = $releaseInfo.tag_name
        
        # Find Windows AMD64 asset
        $asset = $releaseInfo.assets | Where-Object { $_.name -like "*windows-amd64.zip" }
        if (-not $asset) {
            throw "Windows AMD64 asset not found in release"
        }
        
        Write-Log "Found k6 version: $version" -Level INFO
        Write-Log "Downloading from: $($asset.browser_download_url)" -Level INFO
        
        # Create temporary directory
        $tempDir = Join-Path $env:TEMP "k6-install"
        if (Test-Path $tempDir) {
            Remove-Item $tempDir -Recurse -Force
        }
        New-Item -ItemType Directory -Path $tempDir | Out-Null
        
        # Download k6
        $zipPath = Join-Path $tempDir "k6.zip"
        Invoke-WebRequest -Uri $asset.browser_download_url -OutFile $zipPath -UseBasicParsing
        
        # Extract k6
        Write-Log "Extracting k6..." -Level INFO
        Expand-Archive -Path $zipPath -DestinationPath $tempDir -Force
        
        # Find k6.exe in extracted files
        $k6Exe = Get-ChildItem -Path $tempDir -Name "k6.exe" -Recurse | Select-Object -First 1
        if (-not $k6Exe) {
            throw "k6.exe not found in extracted files"
        }
        
        $k6ExePath = Join-Path $tempDir $k6Exe
        
        # Create installation directory
        $installDir = Join-Path $env:ProgramFiles "k6"
        if (-not (Test-Path $installDir)) {
            New-Item -ItemType Directory -Path $installDir -Force | Out-Null
        }
        
        # Copy k6.exe to installation directory
        Copy-Item $k6ExePath -Destination $installDir -Force
        
        # Add to PATH if not already there
        $currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")
        if ($currentPath -notlike "*$installDir*") {
            Write-Log "Adding k6 to system PATH..." -Level INFO
            [Environment]::SetEnvironmentVariable("Path", "$currentPath;$installDir", "Machine")
            $env:Path += ";$installDir"
        }
        
        # Cleanup
        Remove-Item $tempDir -Recurse -Force
        
        Write-Log "k6 installed successfully via direct download" -Level SUCCESS
        return $true
    }
    catch {
        Write-Log "Failed to install k6 via direct download: $($_.Exception.Message)" -Level ERROR
        return $false
    }
}

# Install via Winget
function Install-K6Winget {
    Write-Log "Attempting to install k6 via Winget..." -Level INFO
    
    try {
        # Check if winget is available
        $wingetVersion = & winget --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Winget not found"
        }
        Write-Log "Found Winget version: $wingetVersion" -Level INFO
        
        # Install k6
        & winget install k6 --accept-package-agreements --accept-source-agreements
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "k6 installed successfully via Winget" -Level SUCCESS
            return $true
        } else {
            Write-Log "Failed to install k6 via Winget" -Level ERROR
            return $false
        }
    }
    catch {
        Write-Log "Winget installation failed: $($_.Exception.Message)" -Level ERROR
        return $false
    }
}

# Main installation logic
function Install-K6 {
    Write-Log "Starting k6 installation..." -Level INFO
    Write-Log "Installation method: $Method" -Level INFO
    
    # Check if already installed and not forcing
    if ((Test-K6Installation) -and -not $Force) {
        Write-Log "k6 is already installed. Use -Force to reinstall." -Level INFO
        return $true
    }
    
    $success = $false
    
    switch ($Method) {
        'chocolatey' {
            $success = Install-K6Chocolatey
        }
        'direct' {
            $success = Install-K6Direct
        }
        'winget' {
            $success = Install-K6Winget
        }
        'auto' {
            # Try methods in order of preference
            Write-Log "Auto-detecting best installation method..." -Level INFO
            
            $success = Install-K6Chocolatey
            if (-not $success) {
                Write-Log "Chocolatey installation failed, trying Winget..." -Level WARN
                $success = Install-K6Winget
            }
            if (-not $success) {
                Write-Log "Winget installation failed, trying direct download..." -Level WARN
                $success = Install-K6Direct
            }
        }
    }
    
    if ($success) {
        # Verify installation
        Start-Sleep -Seconds 2
        if (Test-K6Installation) {
            Write-Log "k6 installation completed successfully!" -Level SUCCESS
            
            # Show version
            $version = & k6 version
            Write-Log "Installed version: $($version -split "`n" | Select-Object -First 1)" -Level INFO
            
            return $true
        } else {
            Write-Log "Installation appeared successful but k6 is not accessible" -Level ERROR
            return $false
        }
    } else {
        Write-Log "All installation methods failed" -Level ERROR
        return $false
    }
}

# Run installation
try {
    $result = Install-K6
    if ($result) {
        Write-Log "k6 is ready for use!" -Level SUCCESS
        exit 0
    } else {
        Write-Log "k6 installation failed" -Level ERROR
        exit 1
    }
}
catch {
    Write-Log "Unexpected error during installation: $($_.Exception.Message)" -Level ERROR
    exit 1
}
