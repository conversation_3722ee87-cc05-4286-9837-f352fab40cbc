#Requires -Version 5.1

<#
.SYNOPSIS
    Generates interactive HTML dashboards for load test results
.DESCRIPTION
    This script creates responsive HTML dashboards with Chart.js visualizations
.PARAMETER ResultsDir
    Directory containing processed results
.PARAMETER DashboardDir
    Directory to store generated dashboards
.PARAMETER TestRunId
    Specific test run ID to generate dashboard for
.EXAMPLE
    .\generate-dashboard.ps1 -ResultsDir "../results/processed" -DashboardDir "../dashboard"
#>

param(
    [string]$ResultsDir = "../results/processed",
    [string]$DashboardDir = "../dashboard",
    [string]$TestRunId,
    [switch]$OpenBrowser
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS', 'DEBUG')]
        [string]$Level = 'INFO'
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        'INFO' { 'White' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'SUCCESS' { 'Green' }
        'DEBUG' { 'Gray' }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Load processed results
function Get-ProcessedResults {
    Write-Log "Loading processed results..." -Level INFO
    
    # Look for comparison file
    $comparisonFile = Get-ChildItem -Path $ResultsDir -Filter "performance-comparison.json" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
    
    if (-not $comparisonFile) {
        Write-Log "No comparison results found in $ResultsDir" -Level ERROR
        return $null
    }
    
    try {
        $comparison = Get-Content $comparisonFile.FullName -Raw | ConvertFrom-Json
        Write-Log "Loaded comparison results from: $($comparisonFile.Name)" -Level SUCCESS
        return $comparison
    }
    catch {
        Write-Log "Error loading comparison results: $($_.Exception.Message)" -Level ERROR
        return $null
    }
}

# Generate dashboard data JSON
function New-DashboardData {
    param([object]$Comparison)
    
    Write-Log "Generating dashboard data..." -Level INFO
    
    $dashboardData = @{
        metadata = @{
            generated_at = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            test_timestamp = $Comparison.TestTimestamp
            overall_winner = $Comparison.OverallWinner
            winner_score = $Comparison.WinnerScore
        }
        python = $Comparison.Python
        golang = $Comparison.Golang
        comparison = $Comparison.Comparison
        winner = $Comparison.Winner
        charts = @{
            response_time_comparison = @{
                labels = @("Average", "Median", "P95", "P99")
                python_data = @(
                    $Comparison.Python.AvgResponseTime,
                    $Comparison.Python.MedianResponseTime,
                    $Comparison.Python.P95ResponseTime,
                    $Comparison.Python.P99ResponseTime
                )
                golang_data = @(
                    $Comparison.Golang.AvgResponseTime,
                    $Comparison.Golang.MedianResponseTime,
                    $Comparison.Golang.P95ResponseTime,
                    $Comparison.Golang.P99ResponseTime
                )
            }
            success_rate_comparison = @{
                python = $Comparison.Python.SuccessRate
                golang = $Comparison.Golang.SuccessRate
            }
            throughput_comparison = @{
                python = $Comparison.Python.RequestsPerSecond
                golang = $Comparison.Golang.RequestsPerSecond
            }
            error_rate_comparison = @{
                python = $Comparison.Python.ErrorRate
                golang = $Comparison.Golang.ErrorRate
            }
        }
    }
    
    return $dashboardData
}

# Create CSS file
function New-DashboardCSS {
    $cssContent = @"
/* Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.header h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 300;
}

.header .subtitle {
    color: #7f8c8d;
    font-size: 1.2em;
    margin-bottom: 20px;
}

.winner-badge {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1.1em;
    margin-top: 10px;
}

.winner-python {
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
}

.winner-golang {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

.winner-tie {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.metric-card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

.metric-comparison {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.api-value {
    font-size: 1.1em;
    font-weight: bold;
    padding: 5px 10px;
    border-radius: 5px;
}

.python-value {
    background: #3498db;
    color: white;
}

.golang-value {
    background: #e74c3c;
    color: white;
}

.difference {
    font-size: 0.9em;
    color: #7f8c8d;
    text-align: center;
}

.chart-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
    font-size: 1.4em;
}

.chart-wrapper {
    position: relative;
    height: 400px;
    margin-bottom: 20px;
}

.summary-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.summary-table th,
.summary-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

.summary-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #2c3e50;
}

.summary-table tr:hover {
    background: #f8f9fa;
}

.footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9em;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .metric-comparison {
        flex-direction: column;
        gap: 10px;
    }
    
    .chart-wrapper {
        height: 300px;
    }
}

/* Loading animation */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Export button */
.export-btn {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1em;
    margin: 10px 5px;
    transition: transform 0.2s ease;
}

.export-btn:hover {
    transform: scale(1.05);
}
"@

    $cssPath = Join-Path $DashboardDir "assets/css/dashboard.css"
    $cssContent | Out-File -FilePath $cssPath -Encoding UTF8
    Write-Log "CSS file created: $cssPath" -Level SUCCESS
}

# Create JavaScript file
function New-DashboardJS {
    $jsContent = @"
// Dashboard JavaScript
class DashboardManager {
    constructor() {
        this.data = null;
        this.charts = {};
    }

    async loadData() {
        try {
            const response = await fetch('./data/dashboard-data.json');
            this.data = await response.json();
            console.log('Dashboard data loaded:', this.data);
        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.showError('Failed to load dashboard data');
        }
    }

    showError(message) {
        const container = document.querySelector('.container');
        container.innerHTML = `
            <div class="error-message" style="background: #e74c3c; color: white; padding: 20px; border-radius: 10px; text-align: center;">
                <h2>Error</h2>
                <p>${message}</p>
            </div>
        `;
    }

    updateMetadata() {
        if (!this.data) return;

        document.getElementById('test-timestamp').textContent = this.data.metadata.test_timestamp;
        document.getElementById('generated-at').textContent = this.data.metadata.generated_at;
        
        const winnerBadge = document.getElementById('overall-winner');
        winnerBadge.textContent = `Overall Winner: ${this.data.metadata.overall_winner}`;
        winnerBadge.className = `winner-badge winner-${this.data.metadata.overall_winner.toLowerCase()}`;
        
        document.getElementById('winner-score').textContent = 
            `Python: ${this.data.metadata.winner_score.Python} | Golang: ${this.data.metadata.winner_score.Golang}`;
    }

    createResponseTimeChart() {
        const ctx = document.getElementById('responseTimeChart').getContext('2d');
        
        this.charts.responseTime = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: this.data.charts.response_time_comparison.labels,
                datasets: [
                    {
                        label: 'Python API',
                        data: this.data.charts.response_time_comparison.python_data,
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2
                    },
                    {
                        label: 'Golang API',
                        data: this.data.charts.response_time_comparison.golang_data,
                        backgroundColor: 'rgba(231, 76, 60, 0.8)',
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Response Time Comparison (ms)',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Response Time (ms)'
                        }
                    }
                }
            }
        });
    }

    createSuccessRateChart() {
        const ctx = document.getElementById('successRateChart').getContext('2d');
        
        this.charts.successRate = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Python API', 'Golang API'],
                datasets: [{
                    data: [
                        this.data.charts.success_rate_comparison.python,
                        this.data.charts.success_rate_comparison.golang
                    ],
                    backgroundColor: [
                        'rgba(52, 152, 219, 0.8)',
                        'rgba(231, 76, 60, 0.8)'
                    ],
                    borderColor: [
                        'rgba(52, 152, 219, 1)',
                        'rgba(231, 76, 60, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Success Rate Comparison (%)',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    createThroughputChart() {
        const ctx = document.getElementById('throughputChart').getContext('2d');
        
        this.charts.throughput = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Requests per Second'],
                datasets: [
                    {
                        label: 'Python API',
                        data: [this.data.charts.throughput_comparison.python],
                        backgroundColor: 'rgba(52, 152, 219, 0.8)',
                        borderColor: 'rgba(52, 152, 219, 1)',
                        borderWidth: 2
                    },
                    {
                        label: 'Golang API',
                        data: [this.data.charts.throughput_comparison.golang],
                        backgroundColor: 'rgba(231, 76, 60, 0.8)',
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Throughput Comparison',
                        font: { size: 16 }
                    },
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Requests per Second'
                        }
                    }
                }
            }
        });
    }

    populateMetricsTable() {
        const tableBody = document.getElementById('metrics-table-body');
        const metrics = [
            { name: 'Total Requests', python: this.data.python.TotalRequests, golang: this.data.golang.TotalRequests },
            { name: 'Success Rate (%)', python: this.data.python.SuccessRate, golang: this.data.golang.SuccessRate },
            { name: 'Error Rate (%)', python: this.data.python.ErrorRate, golang: this.data.golang.ErrorRate },
            { name: 'Avg Response Time (ms)', python: this.data.python.AvgResponseTime, golang: this.data.golang.AvgResponseTime },
            { name: 'P95 Response Time (ms)', python: this.data.python.P95ResponseTime, golang: this.data.golang.P95ResponseTime },
            { name: 'P99 Response Time (ms)', python: this.data.python.P99ResponseTime, golang: this.data.golang.P99ResponseTime },
            { name: 'Requests/sec', python: this.data.python.RequestsPerSecond, golang: this.data.golang.RequestsPerSecond }
        ];

        tableBody.innerHTML = '';
        metrics.forEach(metric => {
            const row = document.createElement('tr');
            const winner = this.determineWinner(metric.name, metric.python, metric.golang);
            
            row.innerHTML = `
                <td><strong>${metric.name}</strong></td>
                <td class="${winner === 'python' ? 'python-value' : ''}">${metric.python}</td>
                <td class="${winner === 'golang' ? 'golang-value' : ''}">${metric.golang}</td>
                <td>${winner === 'tie' ? 'Tie' : (winner === 'python' ? 'Python' : 'Golang')}</td>
            `;
            tableBody.appendChild(row);
        });
    }

    determineWinner(metricName, pythonValue, golangValue) {
        if (pythonValue === golangValue) return 'tie';
        
        const lowerIsBetter = ['Error Rate (%)', 'Avg Response Time (ms)', 'P95 Response Time (ms)', 'P99 Response Time (ms)'];
        
        if (lowerIsBetter.includes(metricName)) {
            return pythonValue < golangValue ? 'python' : 'golang';
        } else {
            return pythonValue > golangValue ? 'python' : 'golang';
        }
    }

    exportChart(chartName) {
        const chart = this.charts[chartName];
        if (chart) {
            const url = chart.toBase64Image();
            const link = document.createElement('a');
            link.download = `${chartName}-chart.png`;
            link.href = url;
            link.click();
        }
    }

    async init() {
        await this.loadData();
        if (this.data) {
            this.updateMetadata();
            this.createResponseTimeChart();
            this.createSuccessRateChart();
            this.createThroughputChart();
            this.populateMetricsTable();
        }
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    const dashboard = new DashboardManager();
    dashboard.init();
    
    // Make dashboard globally available for export functions
    window.dashboard = dashboard;
});

// Export functions
function exportResponseTimeChart() {
    window.dashboard.exportChart('responseTime');
}

function exportSuccessRateChart() {
    window.dashboard.exportChart('successRate');
}

function exportThroughputChart() {
    window.dashboard.exportChart('throughput');
}
"@

    $jsPath = Join-Path $DashboardDir "assets/js/dashboard.js"
    $jsContent | Out-File -FilePath $jsPath -Encoding UTF8
    Write-Log "JavaScript file created: $jsPath" -Level SUCCESS
}

# Main dashboard generation function
function New-Dashboard {
    Write-Log "Generating interactive dashboards..." -Level INFO
    
    # Create output directories
    $directories = @(
        $DashboardDir,
        (Join-Path $DashboardDir "assets"),
        (Join-Path $DashboardDir "assets/css"),
        (Join-Path $DashboardDir "assets/js"),
        (Join-Path $DashboardDir "assets/charts"),
        (Join-Path $DashboardDir "data")
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    # Load processed results
    $comparison = Get-ProcessedResults
    if (-not $comparison) {
        Write-Log "Cannot generate dashboard without processed results" -Level ERROR
        return $false
    }
    
    # Generate dashboard data
    $dashboardData = New-DashboardData -Comparison $comparison
    $dataPath = Join-Path $DashboardDir "data/dashboard-data.json"
    $dashboardData | ConvertTo-Json -Depth 10 | Out-File -FilePath $dataPath -Encoding UTF8
    Write-Log "Dashboard data saved: $dataPath" -Level SUCCESS
    
    # Create CSS and JS files
    New-DashboardCSS
    New-DashboardJS
    
    Write-Log "Dashboard generation completed successfully!" -Level SUCCESS
    return $true
}

# Execute main function
try {
    $result = New-Dashboard
    if ($result -and $OpenBrowser) {
        $indexPath = Join-Path $DashboardDir "index.html"
        if (Test-Path $indexPath) {
            Write-Log "Opening dashboard in browser..." -Level INFO
            Start-Process $indexPath
        }
    }
}
catch {
    Write-Log "Unexpected error: $($_.Exception.Message)" -Level ERROR
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level DEBUG
    exit 1
}
