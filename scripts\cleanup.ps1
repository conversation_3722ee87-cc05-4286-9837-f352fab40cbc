#Requires -Version 5.1

<#
.SYNOPSIS
    Cleanup script for load testing environment
.DESCRIPTION
    This script performs cleanup operations including removing temporary files, old results, and log rotation
.PARAMETER CleanResults
    Remove old test results (keeps latest 5 by default)
.PARAMETER CleanLogs
    Clean up log files (keeps latest 10 by default)
.PARAMETER CleanTemp
    Remove temporary files and caches
.PARAMETER KeepLatest
    Number of latest results/logs to keep (default: 5 for results, 10 for logs)
.PARAMETER Force
    Force cleanup without confirmation prompts
.EXAMPLE
    .\cleanup.ps1 -CleanResults -CleanLogs -CleanTemp
.EXAMPLE
    .\cleanup.ps1 -CleanResults -KeepLatest 3 -Force
#>

param(
    [switch]$CleanResults,
    [switch]$CleanLogs,
    [switch]$CleanTemp,
    [int]$KeepLatest = 5,
    [switch]$Force
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS', 'DEBUG')]
        [string]$Level = 'INFO'
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        'INFO' { 'White' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'SUCCESS' { 'Green' }
        'DEBUG' { 'Gray' }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Get user confirmation
function Get-UserConfirmation {
    param(
        [string]$Message,
        [string]$Title = "Confirm Action"
    )
    
    if ($Force) {
        return $true
    }
    
    $choice = $Host.UI.PromptForChoice($Title, $Message, @('&Yes', '&No'), 1)
    return $choice -eq 0
}

# Format file size
function Format-FileSize {
    param([long]$Size)
    
    if ($Size -gt 1GB) {
        return "{0:N2} GB" -f ($Size / 1GB)
    } elseif ($Size -gt 1MB) {
        return "{0:N2} MB" -f ($Size / 1MB)
    } elseif ($Size -gt 1KB) {
        return "{0:N2} KB" -f ($Size / 1KB)
    } else {
        return "$Size bytes"
    }
}

# Clean old test results
function Clear-OldResults {
    Write-Log "Cleaning old test results..." -Level INFO
    
    $resultsDir = "../results"
    if (-not (Test-Path $resultsDir)) {
        Write-Log "Results directory not found: $resultsDir" -Level WARN
        return
    }
    
    # Clean raw results
    $rawDir = Join-Path $resultsDir "raw"
    if (Test-Path $rawDir) {
        $rawFiles = Get-ChildItem -Path $rawDir -Filter "*.json" | Sort-Object LastWriteTime -Descending
        
        if ($rawFiles.Count -gt $KeepLatest) {
            $filesToDelete = $rawFiles | Select-Object -Skip $KeepLatest
            $totalSize = ($filesToDelete | Measure-Object -Property Length -Sum).Sum
            
            Write-Log "Found $($filesToDelete.Count) old raw result files ($(Format-FileSize $totalSize))" -Level INFO
            
            if (Get-UserConfirmation "Delete $($filesToDelete.Count) old raw result files?") {
                foreach ($file in $filesToDelete) {
                    try {
                        Remove-Item $file.FullName -Force
                        Write-Log "Deleted: $($file.Name)" -Level SUCCESS
                    }
                    catch {
                        Write-Log "Failed to delete $($file.Name): $($_.Exception.Message)" -Level ERROR
                    }
                }
            }
        } else {
            Write-Log "No old raw result files to clean (found $($rawFiles.Count), keeping $KeepLatest)" -Level INFO
        }
    }
    
    # Clean processed results
    $processedDir = Join-Path $resultsDir "processed"
    if (Test-Path $processedDir) {
        $processedFiles = Get-ChildItem -Path $processedDir -Filter "*.csv" | Sort-Object LastWriteTime -Descending
        
        if ($processedFiles.Count -gt $KeepLatest) {
            $filesToDelete = $processedFiles | Select-Object -Skip $KeepLatest
            $totalSize = ($filesToDelete | Measure-Object -Property Length -Sum).Sum
            
            Write-Log "Found $($filesToDelete.Count) old processed result files ($(Format-FileSize $totalSize))" -Level INFO
            
            if (Get-UserConfirmation "Delete $($filesToDelete.Count) old processed result files?") {
                foreach ($file in $filesToDelete) {
                    try {
                        Remove-Item $file.FullName -Force
                        Write-Log "Deleted: $($file.Name)" -Level SUCCESS
                    }
                    catch {
                        Write-Log "Failed to delete $($file.Name): $($_.Exception.Message)" -Level ERROR
                    }
                }
            }
        } else {
            Write-Log "No old processed result files to clean (found $($processedFiles.Count), keeping $KeepLatest)" -Level INFO
        }
    }
}

# Clean old log files
function Clear-OldLogs {
    Write-Log "Cleaning old log files..." -Level INFO
    
    $logsDir = "../results/logs"
    if (-not (Test-Path $logsDir)) {
        Write-Log "Logs directory not found: $logsDir" -Level WARN
        return
    }
    
    $logFiles = Get-ChildItem -Path $logsDir -Filter "*.log" | Sort-Object LastWriteTime -Descending
    
    if ($logFiles.Count -gt ($KeepLatest * 2)) { # Keep more logs than results
        $filesToDelete = $logFiles | Select-Object -Skip ($KeepLatest * 2)
        $totalSize = ($filesToDelete | Measure-Object -Property Length -Sum).Sum
        
        Write-Log "Found $($filesToDelete.Count) old log files ($(Format-FileSize $totalSize))" -Level INFO
        
        if (Get-UserConfirmation "Delete $($filesToDelete.Count) old log files?") {
            foreach ($file in $filesToDelete) {
                try {
                    Remove-Item $file.FullName -Force
                    Write-Log "Deleted: $($file.Name)" -Level SUCCESS
                }
                catch {
                    Write-Log "Failed to delete $($file.Name): $($_.Exception.Message)" -Level ERROR
                }
            }
        }
    } else {
        Write-Log "No old log files to clean (found $($logFiles.Count), keeping $($KeepLatest * 2))" -Level INFO
    }
    
    # Rotate large log files
    foreach ($logFile in $logFiles | Select-Object -First ($KeepLatest * 2)) {
        if ($logFile.Length -gt 50MB) {
            Write-Log "Large log file detected: $($logFile.Name) ($(Format-FileSize $logFile.Length))" -Level WARN
            
            if (Get-UserConfirmation "Rotate large log file $($logFile.Name)?") {
                try {
                    $rotatedName = $logFile.Name -replace '\.log$', "-$(Get-Date -Format 'yyyyMMdd-HHmmss').log"
                    $rotatedPath = Join-Path $logFile.Directory $rotatedName
                    Move-Item $logFile.FullName $rotatedPath
                    Write-Log "Rotated to: $rotatedName" -Level SUCCESS
                }
                catch {
                    Write-Log "Failed to rotate $($logFile.Name): $($_.Exception.Message)" -Level ERROR
                }
            }
        }
    }
}

# Clean temporary files
function Clear-TempFiles {
    Write-Log "Cleaning temporary files..." -Level INFO
    
    $tempLocations = @(
        $env:TEMP,
        Join-Path $env:USERPROFILE "AppData\Local\Temp",
        "../dashboard/data/.cache",
        "../results/.temp"
    )
    
    $totalCleaned = 0
    $totalSize = 0
    
    foreach ($tempLocation in $tempLocations) {
        if (-not (Test-Path $tempLocation)) {
            continue
        }
        
        # Look for k6-related temp files
        $k6TempFiles = Get-ChildItem -Path $tempLocation -Filter "*k6*" -Recurse -ErrorAction SilentlyContinue
        $blurTestTempFiles = Get-ChildItem -Path $tempLocation -Filter "*blur-test*" -Recurse -ErrorAction SilentlyContinue
        
        $tempFiles = $k6TempFiles + $blurTestTempFiles
        
        if ($tempFiles.Count -gt 0) {
            $locationSize = ($tempFiles | Measure-Object -Property Length -Sum).Sum
            $totalSize += $locationSize
            
            Write-Log "Found $($tempFiles.Count) temp files in $tempLocation ($(Format-FileSize $locationSize))" -Level INFO
            
            if (Get-UserConfirmation "Delete $($tempFiles.Count) temp files from $tempLocation?") {
                foreach ($file in $tempFiles) {
                    try {
                        if ($file.PSIsContainer) {
                            Remove-Item $file.FullName -Recurse -Force
                        } else {
                            Remove-Item $file.FullName -Force
                        }
                        $totalCleaned++
                    }
                    catch {
                        Write-Log "Failed to delete $($file.Name): $($_.Exception.Message)" -Level WARN
                    }
                }
            }
        }
    }
    
    if ($totalCleaned -gt 0) {
        Write-Log "Cleaned $totalCleaned temporary files ($(Format-FileSize $totalSize))" -Level SUCCESS
    } else {
        Write-Log "No temporary files found to clean" -Level INFO
    }
}

# Clean dashboard cache
function Clear-DashboardCache {
    Write-Log "Cleaning dashboard cache..." -Level INFO
    
    $dashboardDir = "../dashboard"
    if (-not (Test-Path $dashboardDir)) {
        Write-Log "Dashboard directory not found: $dashboardDir" -Level WARN
        return
    }
    
    # Clean old dashboard data files
    $dataDir = Join-Path $dashboardDir "data"
    if (Test-Path $dataDir) {
        $dataFiles = Get-ChildItem -Path $dataDir -Filter "dashboard-data-*.json" | Sort-Object LastWriteTime -Descending
        
        if ($dataFiles.Count -gt 3) { # Keep last 3 dashboard data files
            $filesToDelete = $dataFiles | Select-Object -Skip 3
            
            if (Get-UserConfirmation "Delete $($filesToDelete.Count) old dashboard data files?") {
                foreach ($file in $filesToDelete) {
                    try {
                        Remove-Item $file.FullName -Force
                        Write-Log "Deleted: $($file.Name)" -Level SUCCESS
                    }
                    catch {
                        Write-Log "Failed to delete $($file.Name): $($_.Exception.Message)" -Level ERROR
                    }
                }
            }
        }
    }
    
    # Clean chart cache
    $chartsDir = Join-Path $dashboardDir "assets/charts"
    if (Test-Path $chartsDir) {
        $chartFiles = Get-ChildItem -Path $chartsDir -Filter "*.png"
        
        if ($chartFiles.Count -gt 0) {
            $totalSize = ($chartFiles | Measure-Object -Property Length -Sum).Sum
            Write-Log "Found $($chartFiles.Count) cached chart files ($(Format-FileSize $totalSize))" -Level INFO
            
            if (Get-UserConfirmation "Delete $($chartFiles.Count) cached chart files?") {
                foreach ($file in $chartFiles) {
                    try {
                        Remove-Item $file.FullName -Force
                        Write-Log "Deleted: $($file.Name)" -Level SUCCESS
                    }
                    catch {
                        Write-Log "Failed to delete $($file.Name): $($_.Exception.Message)" -Level ERROR
                    }
                }
            }
        }
    }
}

# Display cleanup summary
function Show-CleanupSummary {
    Write-Log "Cleanup Summary" -Level INFO
    Write-Log "===============" -Level INFO
    
    # Calculate current disk usage
    $directories = @("../results", "../dashboard")
    $totalSize = 0
    
    foreach ($dir in $directories) {
        if (Test-Path $dir) {
            $dirSize = (Get-ChildItem -Path $dir -Recurse -File | Measure-Object -Property Length -Sum).Sum
            $totalSize += $dirSize
            Write-Log "$dir: $(Format-FileSize $dirSize)" -Level INFO
        }
    }
    
    Write-Log "Total project size: $(Format-FileSize $totalSize)" -Level INFO
    
    # Recommendations
    Write-Log "" -Level INFO
    Write-Log "Recommendations:" -Level INFO
    Write-Log "- Run cleanup regularly to maintain optimal performance" -Level INFO
    Write-Log "- Consider archiving important test results before cleanup" -Level INFO
    Write-Log "- Monitor disk space if running frequent tests" -Level INFO
}

# Main cleanup function
function Start-Cleanup {
    Write-Log "Starting cleanup operations..." -Level INFO
    Write-Log "Keep latest: $KeepLatest" -Level INFO
    Write-Log "Force mode: $Force" -Level INFO
    
    if (-not ($CleanResults -or $CleanLogs -or $CleanTemp)) {
        Write-Log "No cleanup operations specified. Use -CleanResults, -CleanLogs, or -CleanTemp" -Level WARN
        Write-Log "Available options:" -Level INFO
        Write-Log "  -CleanResults  : Remove old test result files" -Level INFO
        Write-Log "  -CleanLogs     : Remove old log files" -Level INFO
        Write-Log "  -CleanTemp     : Remove temporary files" -Level INFO
        Write-Log "  -Force         : Skip confirmation prompts" -Level INFO
        Write-Log "  -KeepLatest N  : Keep N latest files (default: 5)" -Level INFO
        return
    }
    
    try {
        if ($CleanResults) {
            Clear-OldResults
        }
        
        if ($CleanLogs) {
            Clear-OldLogs
        }
        
        if ($CleanTemp) {
            Clear-TempFiles
            Clear-DashboardCache
        }
        
        Show-CleanupSummary
        Write-Log "Cleanup completed successfully!" -Level SUCCESS
    }
    catch {
        Write-Log "Error during cleanup: $($_.Exception.Message)" -Level ERROR
        throw
    }
}

# Execute cleanup
try {
    Start-Cleanup
}
catch {
    Write-Log "Unexpected error during cleanup: $($_.Exception.Message)" -Level ERROR
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level DEBUG
    exit 1
}
