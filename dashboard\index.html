<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blur Detection API Performance Comparison</title>
    <link rel="stylesheet" href="assets/css/dashboard.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <h1>🔍 Blur Detection API Performance Comparison</h1>
            <p class="subtitle">Comprehensive Load Testing Analysis Dashboard</p>
            <div id="overall-winner" class="winner-badge">Loading...</div>
            <div style="margin-top: 15px;">
                <small>Test Run: <span id="test-timestamp">Loading...</span></small><br>
                <small>Generated: <span id="generated-at">Loading...</span></small><br>
                <small>Score: <span id="winner-score">Loading...</span></small>
            </div>
        </div>

        <!-- Quick Metrics Overview -->
        <div class="metrics-grid">
            <div class="metric-card">
                <h3>📈 Response Time Performance</h3>
                <div class="metric-comparison">
                    <div>
                        <div class="api-value python-value">Python API</div>
                        <div id="python-avg-response">Loading...</div>
                    </div>
                    <div class="difference">vs</div>
                    <div>
                        <div class="api-value golang-value">Golang API</div>
                        <div id="golang-avg-response">Loading...</div>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <h3>✅ Success Rate</h3>
                <div class="metric-comparison">
                    <div>
                        <div class="api-value python-value">Python API</div>
                        <div id="python-success-rate">Loading...</div>
                    </div>
                    <div class="difference">vs</div>
                    <div>
                        <div class="api-value golang-value">Golang API</div>
                        <div id="golang-success-rate">Loading...</div>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <h3>🚀 Throughput</h3>
                <div class="metric-comparison">
                    <div>
                        <div class="api-value python-value">Python API</div>
                        <div id="python-throughput">Loading...</div>
                    </div>
                    <div class="difference">vs</div>
                    <div>
                        <div class="api-value golang-value">Golang API</div>
                        <div id="golang-throughput">Loading...</div>
                    </div>
                </div>
            </div>

            <div class="metric-card">
                <h3>⚠️ Error Rate</h3>
                <div class="metric-comparison">
                    <div>
                        <div class="api-value python-value">Python API</div>
                        <div id="python-error-rate">Loading...</div>
                    </div>
                    <div class="difference">vs</div>
                    <div>
                        <div class="api-value golang-value">Golang API</div>
                        <div id="golang-error-rate">Loading...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="chart-container">
            <h3>📊 Response Time Comparison</h3>
            <div class="chart-wrapper">
                <canvas id="responseTimeChart"></canvas>
            </div>
            <button class="export-btn" onclick="exportResponseTimeChart()">📥 Export Chart</button>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
            <div class="chart-container">
                <h3>✅ Success Rate Comparison</h3>
                <div class="chart-wrapper">
                    <canvas id="successRateChart"></canvas>
                </div>
                <button class="export-btn" onclick="exportSuccessRateChart()">📥 Export Chart</button>
            </div>

            <div class="chart-container">
                <h3>🚀 Throughput Comparison</h3>
                <div class="chart-wrapper">
                    <canvas id="throughputChart"></canvas>
                </div>
                <button class="export-btn" onclick="exportThroughputChart()">📥 Export Chart</button>
            </div>
        </div>

        <!-- Detailed Metrics Table -->
        <div class="chart-container">
            <h3>📋 Detailed Performance Metrics</h3>
            <table class="summary-table">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Python API</th>
                        <th>Golang API</th>
                        <th>Winner</th>
                    </tr>
                </thead>
                <tbody id="metrics-table-body">
                    <tr>
                        <td colspan="4" style="text-align: center;">
                            <div class="loading">
                                <div class="spinner"></div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Navigation Links -->
        <div class="chart-container">
            <h3>🔗 Additional Dashboards</h3>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <a href="python-api.html" class="export-btn" style="text-decoration: none;">🐍 Python API Details</a>
                <a href="golang-api.html" class="export-btn" style="text-decoration: none;">🔷 Golang API Details</a>
                <button class="export-btn" onclick="window.print()">🖨️ Print Report</button>
                <button class="export-btn" onclick="location.reload()">🔄 Refresh Data</button>
            </div>
        </div>

        <!-- Performance Insights -->
        <div class="chart-container">
            <h3>💡 Performance Insights</h3>
            <div id="insights-content">
                <div class="loading">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>Generated by Blur Detection API Load Testing Suite | Powered by k6 & Chart.js</p>
        <p>📊 Dashboard automatically updates with latest test results</p>
    </div>

    <script src="assets/js/dashboard.js"></script>
    <script>
        // Additional dashboard functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Update quick metrics when data loads
            setTimeout(() => {
                if (window.dashboard && window.dashboard.data) {
                    const data = window.dashboard.data;
                    
                    // Update quick metrics
                    document.getElementById('python-avg-response').textContent = data.python.AvgResponseTime + ' ms';
                    document.getElementById('golang-avg-response').textContent = data.golang.AvgResponseTime + ' ms';
                    document.getElementById('python-success-rate').textContent = data.python.SuccessRate + '%';
                    document.getElementById('golang-success-rate').textContent = data.golang.SuccessRate + '%';
                    document.getElementById('python-throughput').textContent = data.python.RequestsPerSecond + ' req/s';
                    document.getElementById('golang-throughput').textContent = data.golang.RequestsPerSecond + ' req/s';
                    document.getElementById('python-error-rate').textContent = data.python.ErrorRate + '%';
                    document.getElementById('golang-error-rate').textContent = data.golang.ErrorRate + '%';
                    
                    // Generate insights
                    generateInsights(data);
                }
            }, 1000);
        });

        function generateInsights(data) {
            const insights = [];
            
            // Response time insights
            const pythonAvg = data.python.AvgResponseTime;
            const golangAvg = data.golang.AvgResponseTime;
            const responseDiff = Math.abs(pythonAvg - golangAvg);
            const responsePctDiff = ((responseDiff / Math.min(pythonAvg, golangAvg)) * 100).toFixed(1);
            
            if (pythonAvg < golangAvg) {
                insights.push(`🏆 Python API is ${responsePctDiff}% faster in average response time (${pythonAvg}ms vs ${golangAvg}ms)`);
            } else if (golangAvg < pythonAvg) {
                insights.push(`🏆 Golang API is ${responsePctDiff}% faster in average response time (${golangAvg}ms vs ${pythonAvg}ms)`);
            } else {
                insights.push(`⚖️ Both APIs have identical average response times (${pythonAvg}ms)`);
            }
            
            // Success rate insights
            if (data.python.SuccessRate > data.golang.SuccessRate) {
                insights.push(`✅ Python API has higher reliability (${data.python.SuccessRate}% vs ${data.golang.SuccessRate}% success rate)`);
            } else if (data.golang.SuccessRate > data.python.SuccessRate) {
                insights.push(`✅ Golang API has higher reliability (${data.golang.SuccessRate}% vs ${data.python.SuccessRate}% success rate)`);
            }
            
            // Throughput insights
            if (data.python.RequestsPerSecond > data.golang.RequestsPerSecond) {
                insights.push(`🚀 Python API handles more requests per second (${data.python.RequestsPerSecond} vs ${data.golang.RequestsPerSecond})`);
            } else if (data.golang.RequestsPerSecond > data.python.RequestsPerSecond) {
                insights.push(`🚀 Golang API handles more requests per second (${data.golang.RequestsPerSecond} vs ${data.python.RequestsPerSecond})`);
            }
            
            // P99 insights
            if (data.python.P99ResponseTime < data.golang.P99ResponseTime) {
                insights.push(`⚡ Python API has better worst-case performance (P99: ${data.python.P99ResponseTime}ms vs ${data.golang.P99ResponseTime}ms)`);
            } else if (data.golang.P99ResponseTime < data.python.P99ResponseTime) {
                insights.push(`⚡ Golang API has better worst-case performance (P99: ${data.golang.P99ResponseTime}ms vs ${data.python.P99ResponseTime}ms)`);
            }
            
            // Overall recommendation
            const overallWinner = data.metadata.overall_winner;
            if (overallWinner !== 'Tie') {
                insights.push(`🎯 Recommendation: ${overallWinner} API shows better overall performance across multiple metrics`);
            } else {
                insights.push(`🤝 Both APIs show comparable performance - choice may depend on specific requirements`);
            }
            
            // Display insights
            const insightsContainer = document.getElementById('insights-content');
            insightsContainer.innerHTML = insights.map(insight => `<p style="margin-bottom: 10px; padding: 10px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #3498db;">${insight}</p>`).join('');
        }
    </script>
</body>
</html>
