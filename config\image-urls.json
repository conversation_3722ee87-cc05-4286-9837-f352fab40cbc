{"description": "Sample image URLs for blur detection testing", "last_updated": "2025-09-15", "total_images": 20, "image_urls": ["https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop", "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop", "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&blur=2", "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=800&h=600&fit=crop", "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop&blur=5", "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=800&fit=crop", "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=800&h=600&fit=crop&blur=3", "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1200&h=800&fit=crop", "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop", "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=1200&h=800&fit=crop&blur=1", "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=300&fit=crop&blur=4", "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1600&h=1200&fit=crop", "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=300&fit=crop", "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1600&h=1200&fit=crop&blur=2", "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&blur=1", "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=1600&h=1200&fit=crop&blur=3", "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800&h=600&fit=crop&blur=1", "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=800&fit=crop&blur=4", "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=1200&h=800&fit=crop&blur=2", "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=1600&h=1200&fit=crop&blur=3"], "categories": {"sharp_images": [0, 1, 3, 5, 7, 8, 11, 12], "blurred_images": [2, 4, 6, 9, 10, 13, 14, 15, 16, 17, 18, 19], "small_resolution": [8, 10, 12], "medium_resolution": [0, 1, 2, 3, 4, 14, 16], "high_resolution": [5, 7, 9, 11, 13, 15, 17, 18, 19]}, "metadata": {"source": "Unsplash API with various blur and resolution parameters", "test_coverage": "Mix of sharp and blurred images across different resolutions", "expected_behavior": "Sharp images should be detected as non-blurred, blurred images should be detected as blurred"}}