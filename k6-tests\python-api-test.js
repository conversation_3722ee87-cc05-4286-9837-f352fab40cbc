import http from 'k6/http';
import { check, sleep } from 'k6';
import { SharedArray } from 'k6/data';
import { 
    validateImageUrl, 
    validateResponse, 
    makeRequestWithRetry, 
    createRequestPayload,
    logTestResult,
    errorRate,
    successRate
} from './utils.js';

// Load configuration and test data
const config = JSON.parse(open('../config/test-config.json'));
const imageData = JSON.parse(open('../config/image-urls.json'));

// Shared array for image URLs (efficient memory usage across VUs)
const imageUrls = new SharedArray('image_urls', function () {
    return imageData.image_urls.filter(url => validateImageUrl(url));
});

// Test configuration
export const options = {
    stages: [
        { duration: config.test_parameters.ramp_up_time, target: config.test_parameters.virtual_users_per_api },
        { duration: config.test_parameters.duration, target: config.test_parameters.virtual_users_per_api },
        { duration: '30s', target: 0 }, // Ramp down
    ],
    thresholds: {
        http_req_duration: ['p(95)<30000'], // 95% of requests must complete within 30s
        http_req_failed: ['rate<0.2'], // Error rate should be less than 20%
        errors: ['rate<0.2'],
        success: ['rate>0.8'],
    },
    tags: {
        api: 'python',
        test_type: 'load_test'
    }
};

// Setup function - runs once before the test
export function setup() {
    console.log('Starting Python API load test...');
    console.log(`Test duration: ${config.test_parameters.duration}`);
    console.log(`Virtual users: ${config.test_parameters.virtual_users_per_api}`);
    console.log(`Total images to test: ${imageUrls.length}`);
    
    // Connectivity check
    if (config.validation.pre_test_connectivity_check) {
        console.log('Performing connectivity check...');
        const testUrl = imageUrls[0];
        const payload = createRequestPayload('python', testUrl);
        
        try {
            const response = http.post(config.apis.python.endpoint, payload, {
                headers: config.apis.python.headers,
                timeout: '10s'
            });
            
            if (response.status === 200) {
                console.log('✓ Python API connectivity check passed');
            } else {
                console.warn(`⚠ Python API connectivity check returned status: ${response.status}`);
            }
        } catch (error) {
            console.error(`✗ Python API connectivity check failed: ${error.message}`);
        }
    }
    
    return {
        startTime: Date.now(),
        imageUrls: imageUrls,
        config: config
    };
}

// Main test function - runs for each VU iteration
export default function (data) {
    const vuId = __VU;
    const iteration = __ITER;
    
    // Select image URL (round-robin with some randomization)
    const imageIndex = (iteration + vuId) % data.imageUrls.length;
    const imageUrl = data.imageUrls[imageIndex];
    
    // Create request payload
    const payload = createRequestPayload('python', imageUrl);
    
    // Prepare request parameters
    const params = {
        body: payload,
        headers: data.config.apis.python.headers,
        timeout: data.config.test_parameters.request_timeout
    };
    
    // Make request with retry logic
    const response = makeRequestWithRetry(
        data.config.apis.python.endpoint,
        params,
        'python',
        data.config.test_parameters.max_retries
    );
    
    // Validate response
    const isValid = validateResponse(response, 'python');
    
    // Log detailed result
    logTestResult(response, 'python', imageUrl, vuId, iteration);
    
    // Additional checks specific to Python API
    if (response.status === 200) {
        check(response, {
            'python api response contains blur_detected field': (r) => {
                try {
                    const body = JSON.parse(r.body);
                    return body.hasOwnProperty('blur_detected');
                } catch (e) {
                    return false;
                }
            },
            'python api blur_detected is boolean': (r) => {
                try {
                    const body = JSON.parse(r.body);
                    return typeof body.blur_detected === 'boolean';
                } catch (e) {
                    return false;
                }
            }
        });
    }
    
    // Think time between requests
    const thinkTime = parseFloat(data.config.test_parameters.think_time.replace('s', ''));
    if (thinkTime > 0) {
        sleep(thinkTime);
    }
}

// Teardown function - runs once after the test
export function teardown(data) {
    const endTime = Date.now();
    const totalDuration = (endTime - data.startTime) / 1000;
    
    console.log('Python API load test completed');
    console.log(`Total test duration: ${totalDuration.toFixed(2)} seconds`);
    console.log(`Images tested: ${data.imageUrls.length}`);
    
    // Final summary will be handled by k6's built-in summary
}
