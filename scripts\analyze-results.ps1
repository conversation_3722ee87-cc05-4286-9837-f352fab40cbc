#Requires -Version 5.1

<#
.SYNOPSIS
    Analyzes k6 test results and generates performance metrics
.DESCRIPTION
    This script processes raw k6 JSON output files and generates comprehensive performance analysis
.PARAMETER ResultsDir
    Directory containing raw k6 results
.PARAMETER OutputDir
    Directory to store processed results
.PARAMETER TestRunId
    Specific test run ID to analyze (optional, analyzes latest if not specified)
.EXAMPLE
    .\analyze-results.ps1 -ResultsDir "../results/raw" -OutputDir "../results/processed"
#>

param(
    [string]$ResultsDir = "../results/raw",
    [string]$OutputDir = "../results/processed",
    [string]$TestRunId,
    [switch]$GenerateCSV = $true
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS', 'DEBUG')]
        [string]$Level = 'INFO'
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        'INFO' { 'White' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'SUCCESS' { 'Green' }
        'DEBUG' { 'Gray' }
    }
    
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

# Find latest test results
function Get-LatestTestResults {
    $pythonFiles = Get-ChildItem -Path $ResultsDir -Filter "python-api-*.json" | Sort-Object LastWriteTime -Descending
    $golangFiles = Get-ChildItem -Path $ResultsDir -Filter "golang-api-*.json" | Sort-Object LastWriteTime -Descending
    
    if ($TestRunId) {
        $pythonFile = $pythonFiles | Where-Object { $_.Name -like "*$TestRunId*" } | Select-Object -First 1
        $golangFile = $golangFiles | Where-Object { $_.Name -like "*$TestRunId*" } | Select-Object -First 1
    } else {
        $pythonFile = $pythonFiles | Select-Object -First 1
        $golangFile = $golangFiles | Select-Object -First 1
    }
    
    return @{
        Python = $pythonFile
        Golang = $golangFile
    }
}

# Parse k6 JSON results
function Parse-K6Results {
    param(
        [string]$FilePath,
        [string]$ApiName
    )
    
    Write-Log "Parsing $ApiName results from: $FilePath" -Level INFO
    
    if (-not (Test-Path $FilePath)) {
        Write-Log "Results file not found: $FilePath" -Level ERROR
        return $null
    }
    
    $results = @{
        ApiName = $ApiName
        FilePath = $FilePath
        Metrics = @{}
        RequestData = @()
        Summary = @{}
    }
    
    try {
        $lines = Get-Content $FilePath
        $httpReqDurations = @()
        $httpReqFailed = @()
        $timestamps = @()
        $statusCodes = @{}
        $errors = @()
        
        foreach ($line in $lines) {
            if ($line.Trim() -eq '') { continue }
            
            try {
                $data = $line | ConvertFrom-Json
                
                # Process different metric types
                switch ($data.type) {
                    'Point' {
                        $metricName = $data.metric
                        $value = $data.data.value
                        $timestamp = $data.data.time
                        
                        switch ($metricName) {
                            'http_req_duration' {
                                $httpReqDurations += $value
                                $timestamps += [DateTime]::Parse($timestamp)
                            }
                            'http_req_failed' {
                                $httpReqFailed += $value
                            }
                            'http_req_receiving' {
                                # Additional metric processing
                            }
                        }
                        
                        # Collect status codes
                        if ($data.data.tags -and $data.data.tags.status) {
                            $status = $data.data.tags.status
                            if ($statusCodes.ContainsKey($status)) {
                                $statusCodes[$status]++
                            } else {
                                $statusCodes[$status] = 1
                            }
                        }
                    }
                }
            }
            catch {
                # Skip malformed lines
                continue
            }
        }
        
        # Calculate metrics
        if ($httpReqDurations.Count -gt 0) {
            $sortedDurations = $httpReqDurations | Sort-Object
            $totalRequests = $httpReqDurations.Count
            $failedRequests = ($httpReqFailed | Measure-Object -Sum).Sum
            $successfulRequests = $totalRequests - $failedRequests
            
            $results.Metrics = @{
                TotalRequests = $totalRequests
                SuccessfulRequests = $successfulRequests
                FailedRequests = $failedRequests
                SuccessRate = if ($totalRequests -gt 0) { [math]::Round(($successfulRequests / $totalRequests) * 100, 2) } else { 0 }
                ErrorRate = if ($totalRequests -gt 0) { [math]::Round(($failedRequests / $totalRequests) * 100, 2) } else { 0 }
                
                # Response time metrics (in milliseconds)
                AvgResponseTime = [math]::Round(($httpReqDurations | Measure-Object -Average).Average, 2)
                MinResponseTime = [math]::Round(($httpReqDurations | Measure-Object -Minimum).Minimum, 2)
                MaxResponseTime = [math]::Round(($httpReqDurations | Measure-Object -Maximum).Maximum, 2)
                MedianResponseTime = [math]::Round($sortedDurations[[math]::Floor($sortedDurations.Count / 2)], 2)
                
                # Percentiles
                P90ResponseTime = [math]::Round($sortedDurations[[math]::Floor($sortedDurations.Count * 0.9)], 2)
                P95ResponseTime = [math]::Round($sortedDurations[[math]::Floor($sortedDurations.Count * 0.95)], 2)
                P99ResponseTime = [math]::Round($sortedDurations[[math]::Floor($sortedDurations.Count * 0.99)], 2)
                
                # Throughput
                TestDurationSeconds = if ($timestamps.Count -gt 1) { 
                    ($timestamps | Measure-Object -Maximum).Maximum.Subtract(($timestamps | Measure-Object -Minimum).Minimum).TotalSeconds 
                } else { 1 }
            }
            
            # Calculate RPS
            $results.Metrics.RequestsPerSecond = if ($results.Metrics.TestDurationSeconds -gt 0) {
                [math]::Round($totalRequests / $results.Metrics.TestDurationSeconds, 2)
            } else { 0 }
            
            $results.Metrics.SuccessfulRequestsPerSecond = if ($results.Metrics.TestDurationSeconds -gt 0) {
                [math]::Round($successfulRequests / $results.Metrics.TestDurationSeconds, 2)
            } else { 0 }
            
            # Status code distribution
            $results.Metrics.StatusCodes = $statusCodes
        }
        
        Write-Log "$ApiName analysis completed - $($results.Metrics.TotalRequests) requests processed" -Level SUCCESS
        return $results
    }
    catch {
        Write-Log "Error parsing $ApiName results: $($_.Exception.Message)" -Level ERROR
        return $null
    }
}

# Generate comparison analysis
function New-ComparisonAnalysis {
    param(
        [object]$PythonResults,
        [object]$GolangResults
    )
    
    Write-Log "Generating comparison analysis..." -Level INFO
    
    $comparison = @{
        TestTimestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        Python = $PythonResults.Metrics
        Golang = $GolangResults.Metrics
        Comparison = @{}
        Winner = @{}
    }
    
    # Compare key metrics
    $metrics = @(
        'AvgResponseTime', 'MedianResponseTime', 'P95ResponseTime', 'P99ResponseTime',
        'SuccessRate', 'ErrorRate', 'RequestsPerSecond', 'SuccessfulRequestsPerSecond'
    )
    
    foreach ($metric in $metrics) {
        $pythonValue = $PythonResults.Metrics[$metric]
        $golangValue = $GolangResults.Metrics[$metric]
        
        if ($pythonValue -ne $null -and $golangValue -ne $null) {
            $difference = $pythonValue - $golangValue
            $percentDifference = if ($golangValue -ne 0) {
                [math]::Round((($pythonValue - $golangValue) / $golangValue) * 100, 2)
            } else { 0 }
            
            $comparison.Comparison[$metric] = @{
                Python = $pythonValue
                Golang = $golangValue
                Difference = [math]::Round($difference, 2)
                PercentDifference = $percentDifference
            }
            
            # Determine winner (lower is better for response times and error rate, higher is better for success rate and RPS)
            $lowerIsBetter = @('AvgResponseTime', 'MedianResponseTime', 'P95ResponseTime', 'P99ResponseTime', 'ErrorRate')
            
            if ($metric -in $lowerIsBetter) {
                $comparison.Winner[$metric] = if ($pythonValue -lt $golangValue) { 'Python' } elseif ($golangValue -lt $pythonValue) { 'Golang' } else { 'Tie' }
            } else {
                $comparison.Winner[$metric] = if ($pythonValue -gt $golangValue) { 'Python' } elseif ($golangValue -gt $pythonValue) { 'Golang' } else { 'Tie' }
            }
        }
    }
    
    # Overall winner calculation
    $pythonWins = ($comparison.Winner.Values | Where-Object { $_ -eq 'Python' }).Count
    $golangWins = ($comparison.Winner.Values | Where-Object { $_ -eq 'Golang' }).Count
    
    $comparison.OverallWinner = if ($pythonWins -gt $golangWins) { 'Python' } elseif ($golangWins -gt $pythonWins) { 'Golang' } else { 'Tie' }
    $comparison.WinnerScore = @{
        Python = $pythonWins
        Golang = $golangWins
        Total = $pythonWins + $golangWins
    }
    
    Write-Log "Comparison analysis completed - Overall winner: $($comparison.OverallWinner)" -Level SUCCESS
    return $comparison
}

# Export results to CSV
function Export-ResultsToCSV {
    param(
        [object]$PythonResults,
        [object]$GolangResults,
        [object]$Comparison,
        [string]$OutputPath
    )
    
    Write-Log "Exporting results to CSV..." -Level INFO
    
    # Create summary CSV
    $summaryData = @()
    
    # Add Python metrics
    $pythonRow = [PSCustomObject]@{
        API = 'Python'
        TotalRequests = $PythonResults.Metrics.TotalRequests
        SuccessfulRequests = $PythonResults.Metrics.SuccessfulRequests
        FailedRequests = $PythonResults.Metrics.FailedRequests
        SuccessRate = $PythonResults.Metrics.SuccessRate
        ErrorRate = $PythonResults.Metrics.ErrorRate
        AvgResponseTime = $PythonResults.Metrics.AvgResponseTime
        MedianResponseTime = $PythonResults.Metrics.MedianResponseTime
        P90ResponseTime = $PythonResults.Metrics.P90ResponseTime
        P95ResponseTime = $PythonResults.Metrics.P95ResponseTime
        P99ResponseTime = $PythonResults.Metrics.P99ResponseTime
        RequestsPerSecond = $PythonResults.Metrics.RequestsPerSecond
        SuccessfulRequestsPerSecond = $PythonResults.Metrics.SuccessfulRequestsPerSecond
    }
    
    # Add Golang metrics
    $golangRow = [PSCustomObject]@{
        API = 'Golang'
        TotalRequests = $GolangResults.Metrics.TotalRequests
        SuccessfulRequests = $GolangResults.Metrics.SuccessfulRequests
        FailedRequests = $GolangResults.Metrics.FailedRequests
        SuccessRate = $GolangResults.Metrics.SuccessRate
        ErrorRate = $GolangResults.Metrics.ErrorRate
        AvgResponseTime = $GolangResults.Metrics.AvgResponseTime
        MedianResponseTime = $GolangResults.Metrics.MedianResponseTime
        P90ResponseTime = $GolangResults.Metrics.P90ResponseTime
        P95ResponseTime = $GolangResults.Metrics.P95ResponseTime
        P99ResponseTime = $GolangResults.Metrics.P99ResponseTime
        RequestsPerSecond = $GolangResults.Metrics.RequestsPerSecond
        SuccessfulRequestsPerSecond = $GolangResults.Metrics.SuccessfulRequestsPerSecond
    }
    
    $summaryData += $pythonRow
    $summaryData += $golangRow
    
    # Export summary
    $summaryPath = Join-Path $OutputPath "performance-summary.csv"
    $summaryData | Export-Csv -Path $summaryPath -NoTypeInformation
    Write-Log "Summary exported to: $summaryPath" -Level SUCCESS
    
    # Export comparison
    $comparisonPath = Join-Path $OutputPath "performance-comparison.json"
    $Comparison | ConvertTo-Json -Depth 10 | Out-File -FilePath $comparisonPath -Encoding UTF8
    Write-Log "Comparison exported to: $comparisonPath" -Level SUCCESS
}

# Main analysis function
function Start-ResultsAnalysis {
    Write-Log "Starting results analysis..." -Level INFO
    Write-Log "Results directory: $ResultsDir" -Level INFO
    Write-Log "Output directory: $OutputDir" -Level INFO
    
    # Create output directory
    if (-not (Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
    }
    
    # Find test result files
    $resultFiles = Get-LatestTestResults
    
    if (-not $resultFiles.Python -or -not $resultFiles.Golang) {
        Write-Log "Could not find both Python and Golang result files" -Level ERROR
        Write-Log "Python: $($resultFiles.Python)" -Level DEBUG
        Write-Log "Golang: $($resultFiles.Golang)" -Level DEBUG
        exit 1
    }
    
    # Parse results
    $pythonResults = Parse-K6Results -FilePath $resultFiles.Python.FullName -ApiName "Python"
    $golangResults = Parse-K6Results -FilePath $resultFiles.Golang.FullName -ApiName "Golang"
    
    if (-not $pythonResults -or -not $golangResults) {
        Write-Log "Failed to parse one or more result files" -Level ERROR
        exit 1
    }
    
    # Generate comparison
    $comparison = New-ComparisonAnalysis -PythonResults $pythonResults -GolangResults $golangResults
    
    # Export results
    if ($GenerateCSV) {
        Export-ResultsToCSV -PythonResults $pythonResults -GolangResults $golangResults -Comparison $comparison -OutputPath $OutputDir
    }
    
    # Display summary
    Write-Log "=== PERFORMANCE ANALYSIS SUMMARY ===" -Level SUCCESS
    Write-Log "Python API:" -Level INFO
    Write-Log "  Total Requests: $($pythonResults.Metrics.TotalRequests)" -Level INFO
    Write-Log "  Success Rate: $($pythonResults.Metrics.SuccessRate)%" -Level INFO
    Write-Log "  Avg Response Time: $($pythonResults.Metrics.AvgResponseTime)ms" -Level INFO
    Write-Log "  P95 Response Time: $($pythonResults.Metrics.P95ResponseTime)ms" -Level INFO
    Write-Log "  Requests/sec: $($pythonResults.Metrics.RequestsPerSecond)" -Level INFO
    
    Write-Log "Golang API:" -Level INFO
    Write-Log "  Total Requests: $($golangResults.Metrics.TotalRequests)" -Level INFO
    Write-Log "  Success Rate: $($golangResults.Metrics.SuccessRate)%" -Level INFO
    Write-Log "  Avg Response Time: $($golangResults.Metrics.AvgResponseTime)ms" -Level INFO
    Write-Log "  P95 Response Time: $($golangResults.Metrics.P95ResponseTime)ms" -Level INFO
    Write-Log "  Requests/sec: $($golangResults.Metrics.RequestsPerSecond)" -Level INFO
    
    Write-Log "Overall Winner: $($comparison.OverallWinner)" -Level SUCCESS
    Write-Log "Score - Python: $($comparison.WinnerScore.Python), Golang: $($comparison.WinnerScore.Golang)" -Level INFO
    
    Write-Log "Analysis completed successfully!" -Level SUCCESS
    Write-Log "Next step: Run generate-dashboard.ps1 to create visualization dashboards" -Level INFO
}

# Execute main function
try {
    Start-ResultsAnalysis
}
catch {
    Write-Log "Unexpected error: $($_.Exception.Message)" -Level ERROR
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level DEBUG
    exit 1
}
