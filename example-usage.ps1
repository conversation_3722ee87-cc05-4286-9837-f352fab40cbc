#Requires -Version 5.1

<#
.SYNOPSIS
    Example usage script demonstrating the complete load testing workflow
.DESCRIPTION
    This script demonstrates how to use the blur detection API load testing suite
    from setup to dashboard generation with various configuration options
.PARAMETER Demo
    Run a quick demo with reduced test duration
.PARAMETER FullTest
    Run a comprehensive test with full duration
.PARAMETER SkipSetup
    Skip the setup phase (assumes environment is already configured)
.EXAMPLE
    .\example-usage.ps1 -Demo
.EXAMPLE
    .\example-usage.ps1 -FullTest
#>

param(
    [switch]$Demo,
    [switch]$FullTest,
    [switch]$SkipSetup
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Script configuration
$ScriptName = "Blur Detection API Load Testing - Example Usage"
$ScriptVersion = "1.0.0"

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS', 'HEADER')]
        [string]$Level = 'INFO'
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        'INFO' { 'White' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'SUCCESS' { 'Green' }
        'HEADER' { 'Cyan' }
    }
    
    if ($Level -eq 'HEADER') {
        Write-Host ""
        Write-Host "=" * 60 -ForegroundColor $color
        Write-Host $Message -ForegroundColor $color
        Write-Host "=" * 60 -ForegroundColor $color
        Write-Host ""
    } else {
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

# Display banner
function Show-Banner {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                                                              ║" -ForegroundColor Cyan
    Write-Host "║        Blur Detection API Load Testing - Example Usage      ║" -ForegroundColor Cyan
    Write-Host "║                        Version $ScriptVersion                        ║" -ForegroundColor Cyan
    Write-Host "║                                                              ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
}

# Wait for user input
function Wait-ForUser {
    param([string]$Message = "Press any key to continue...")
    
    Write-Host $Message -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Write-Host ""
}

# Create demo configuration
function New-DemoConfiguration {
    Write-Log "Creating demo configuration..." -Level INFO
    
    # Backup original config
    if (Test-Path "config/test-config.json") {
        Copy-Item "config/test-config.json" "config/test-config.json.backup" -Force
        Write-Log "Original configuration backed up" -Level INFO
    }
    
    # Create demo config with shorter duration and fewer VUs
    $demoConfig = @{
        test_parameters = @{
            duration = "2m"
            ramp_up_time = "15s"
            virtual_users_per_api = 20
            request_timeout = "30s"
            think_time = "1s"
            max_retries = 3
            retry_backoff_base = 2
            retry_backoff_max = "10s"
        }
        apis = @{
            python = @{
                name = "Python Blur Detection API"
                endpoint = "https://blur.lemoncoast-53375c73.centralindia.azurecontainerapps.io/detect-blur"
                method = "POST"
                headers = @{
                    "Content-Type" = "application/json"
                }
                request_body_template = @{
                    image_url = "{{IMAGE_URL}}"
                }
            }
            golang = @{
                name = "Golang Image Analysis API"
                endpoint = "http://fai-imagedescribe.fieldassist.io:8080/analyze"
                method = "POST"
                headers = @{
                    "Content-Type" = "application/json"
                }
                request_body_template = @{
                    url = "{{IMAGE_URL}}"
                    is_ocr = $false
                }
            }
        }
        logging = @{
            level = "INFO"
            max_log_size_mb = 50
            max_log_files = 5
            log_rotation = $true
        }
        output = @{
            raw_format = "json"
            include_response_body = $true
            max_response_body_size = 1024
            export_csv = $true
            export_summary = $true
        }
        validation = @{
            pre_test_connectivity_check = $true
            validate_image_urls = $true
            min_success_rate_threshold = 0.8
            max_error_rate_threshold = 0.2
        }
        dashboard = @{
            auto_refresh_interval = 30
            chart_animation = $true
            export_charts = $true
            responsive_design = $true
        }
    }
    
    $demoConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath "config/test-config.json" -Encoding UTF8
    Write-Log "Demo configuration created (2 minutes, 20 VUs per API)" -Level SUCCESS
}

# Restore original configuration
function Restore-OriginalConfiguration {
    if (Test-Path "config/test-config.json.backup") {
        Move-Item "config/test-config.json.backup" "config/test-config.json" -Force
        Write-Log "Original configuration restored" -Level SUCCESS
    }
}

# Run setup phase
function Invoke-SetupPhase {
    Write-Log "Setup Phase" -Level HEADER
    
    Write-Log "This phase will:" -Level INFO
    Write-Log "  1. Check system requirements" -Level INFO
    Write-Log "  2. Validate project structure" -Level INFO
    Write-Log "  3. Install k6 load testing tool" -Level INFO
    Write-Log "  4. Validate configuration files" -Level INFO
    Write-Log "  5. Test API connectivity" -Level INFO
    
    Wait-ForUser
    
    try {
        & .\setup.ps1 -InstallMethod auto
        if ($LASTEXITCODE -ne 0) {
            throw "Setup failed with exit code $LASTEXITCODE"
        }
        Write-Log "Setup completed successfully!" -Level SUCCESS
    }
    catch {
        Write-Log "Setup failed: $($_.Exception.Message)" -Level ERROR
        throw
    }
}

# Run testing phase
function Invoke-TestingPhase {
    Write-Log "Testing Phase" -Level HEADER
    
    Write-Log "This phase will:" -Level INFO
    Write-Log "  1. Execute load tests against both APIs simultaneously" -Level INFO
    Write-Log "  2. Monitor test progress in real-time" -Level INFO
    Write-Log "  3. Collect comprehensive performance metrics" -Level INFO
    Write-Log "  4. Handle errors and retries automatically" -Level INFO
    
    Wait-ForUser
    
    try {
        & .\scripts\run-tests.ps1 -Parallel
        if ($LASTEXITCODE -ne 0) {
            throw "Testing failed with exit code $LASTEXITCODE"
        }
        Write-Log "Testing completed successfully!" -Level SUCCESS
    }
    catch {
        Write-Log "Testing failed: $($_.Exception.Message)" -Level ERROR
        throw
    }
}

# Run analysis phase
function Invoke-AnalysisPhase {
    Write-Log "Analysis Phase" -Level HEADER
    
    Write-Log "This phase will:" -Level INFO
    Write-Log "  1. Process raw k6 test results" -Level INFO
    Write-Log "  2. Calculate performance metrics and percentiles" -Level INFO
    Write-Log "  3. Generate comparative analysis" -Level INFO
    Write-Log "  4. Export results to CSV format" -Level INFO
    
    Wait-ForUser
    
    try {
        & .\scripts\analyze-results.ps1 -GenerateCSV
        if ($LASTEXITCODE -ne 0) {
            throw "Analysis failed with exit code $LASTEXITCODE"
        }
        Write-Log "Analysis completed successfully!" -Level SUCCESS
    }
    catch {
        Write-Log "Analysis failed: $($_.Exception.Message)" -Level ERROR
        throw
    }
}

# Run dashboard generation phase
function Invoke-DashboardPhase {
    Write-Log "Dashboard Generation Phase" -Level HEADER
    
    Write-Log "This phase will:" -Level INFO
    Write-Log "  1. Generate interactive HTML dashboards" -Level INFO
    Write-Log "  2. Create responsive charts and visualizations" -Level INFO
    Write-Log "  3. Provide detailed performance insights" -Level INFO
    Write-Log "  4. Enable chart export functionality" -Level INFO
    
    Wait-ForUser
    
    try {
        & .\scripts\generate-dashboard.ps1 -OpenBrowser
        if ($LASTEXITCODE -ne 0) {
            throw "Dashboard generation failed with exit code $LASTEXITCODE"
        }
        Write-Log "Dashboard generation completed successfully!" -Level SUCCESS
    }
    catch {
        Write-Log "Dashboard generation failed: $($_.Exception.Message)" -Level ERROR
        throw
    }
}

# Show results summary
function Show-ResultsSummary {
    Write-Log "Results Summary" -Level HEADER
    
    Write-Log "Generated Files:" -Level INFO
    
    # Check for result files
    $resultFiles = @(
        "results/processed/performance-summary.csv",
        "results/processed/performance-comparison.json",
        "dashboard/index.html",
        "dashboard/python-api.html",
        "dashboard/golang-api.html"
    )
    
    foreach ($file in $resultFiles) {
        if (Test-Path $file) {
            $fileInfo = Get-Item $file
            Write-Log "✓ $file ($(($fileInfo.Length / 1KB).ToString('F1')) KB)" -Level SUCCESS
        } else {
            Write-Log "✗ $file (not found)" -Level WARN
        }
    }
    
    Write-Log "" -Level INFO
    Write-Log "Next Steps:" -Level INFO
    Write-Log "  1. Open dashboard/index.html in your web browser" -Level INFO
    Write-Log "  2. Review the performance comparison results" -Level INFO
    Write-Log "  3. Check individual API dashboards for detailed analysis" -Level INFO
    Write-Log "  4. Export charts for presentations or reports" -Level INFO
    Write-Log "  5. Use results/processed/performance-summary.csv for further analysis" -Level INFO
    
    # Open dashboard if available
    $dashboardPath = "dashboard/index.html"
    if (Test-Path $dashboardPath) {
        Write-Log "" -Level INFO
        $openDashboard = Read-Host "Open dashboard in browser now? (y/n)"
        if ($openDashboard -eq 'y' -or $openDashboard -eq 'Y') {
            Start-Process (Resolve-Path $dashboardPath).Path
            Write-Log "Dashboard opened in default browser" -Level SUCCESS
        }
    }
}

# Main execution function
function Start-ExampleUsage {
    Show-Banner
    
    Write-Log "Welcome to the Blur Detection API Load Testing Suite!" -Level INFO
    Write-Log "This example will guide you through the complete testing workflow." -Level INFO
    
    # Determine test mode
    if ($Demo) {
        Write-Log "Running in DEMO mode (shorter duration, fewer virtual users)" -Level WARN
        New-DemoConfiguration
    } elseif ($FullTest) {
        Write-Log "Running in FULL TEST mode (complete load testing)" -Level INFO
    } else {
        Write-Log "Select test mode:" -Level INFO
        Write-Log "  1. Demo mode (2 minutes, 20 VUs per API)" -Level INFO
        Write-Log "  2. Full test mode (5 minutes, 100 VUs per API)" -Level INFO
        
        do {
            $choice = Read-Host "Enter choice (1 or 2)"
        } while ($choice -notin @('1', '2'))
        
        if ($choice -eq '1') {
            $Demo = $true
            New-DemoConfiguration
        } else {
            $FullTest = $true
        }
    }
    
    try {
        # Phase 1: Setup
        if (-not $SkipSetup) {
            Invoke-SetupPhase
        } else {
            Write-Log "Skipping setup phase as requested" -Level WARN
        }
        
        # Phase 2: Testing
        Invoke-TestingPhase
        
        # Phase 3: Analysis
        Invoke-AnalysisPhase
        
        # Phase 4: Dashboard Generation
        Invoke-DashboardPhase
        
        # Phase 5: Results Summary
        Show-ResultsSummary
        
        Write-Log "Example usage completed successfully! 🎉" -Level SUCCESS
    }
    catch {
        Write-Log "Example usage failed: $($_.Exception.Message)" -Level ERROR
        Write-Log "Check the logs for more details" -Level INFO
        exit 1
    }
    finally {
        # Restore original configuration if demo mode was used
        if ($Demo) {
            Restore-OriginalConfiguration
        }
    }
}

# Execute main function
try {
    Start-ExampleUsage
}
catch {
    Write-Log "Unexpected error: $($_.Exception.Message)" -Level ERROR
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level DEBUG
    exit 1
}
