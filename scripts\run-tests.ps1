#Requires -Version 5.1

<#
.SYNOPSIS
    Orchestrates k6 load tests for both Python and Golang blur detection APIs
.DESCRIPTION
    This script runs load tests against both APIs simultaneously and manages the test execution process
.PARAMETER ConfigPath
    Path to the test configuration file
.PARAMETER OutputDir
    Directory to store test results
.PARAMETER Parallel
    Run both API tests in parallel (default: true)
.EXAMPLE
    .\run-tests.ps1 -ConfigPath "../config/test-config.json" -OutputDir "../results"
#>

param(
    [string]$ConfigPath = "../config/test-config.json",
    [string]$OutputDir = "../results",
    [switch]$Parallel = $true,
    [switch]$SkipValidation
)

# Set error action preference
$ErrorActionPreference = 'Stop'

# Import required modules
Add-Type -AssemblyName System.Web

# Logging function
function Write-Log {
    param(
        [string]$Message,
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS', 'DEBUG')]
        [string]$Level = 'INFO'
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $color = switch ($Level) {
        'INFO' { 'White' }
        'WARN' { 'Yellow' }
        'ERROR' { 'Red' }
        'SUCCESS' { 'Green' }
        'DEBUG' { 'Gray' }
    }
    
    $logMessage = "[$timestamp] [$Level] $Message"
    Write-Host $logMessage -ForegroundColor $color
    
    # Also write to log file
    $logFile = Join-Path $OutputDir "logs" "test-execution.log"
    if (Test-Path (Split-Path $logFile)) {
        Add-Content -Path $logFile -Value $logMessage
    }
}

# Validate prerequisites
function Test-Prerequisites {
    Write-Log "Validating prerequisites..." -Level INFO
    
    # Check if k6 is installed
    try {
        $k6Version = & k6 version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "k6 not found"
        }
        Write-Log "k6 is available: $($k6Version -split "`n" | Select-Object -First 1)" -Level SUCCESS
    }
    catch {
        Write-Log "k6 is not installed. Please run install-k6.ps1 first." -Level ERROR
        return $false
    }
    
    # Check configuration file
    if (-not (Test-Path $ConfigPath)) {
        Write-Log "Configuration file not found: $ConfigPath" -Level ERROR
        return $false
    }
    
    # Validate configuration
    try {
        $config = Get-Content $ConfigPath -Raw | ConvertFrom-Json
        Write-Log "Configuration file loaded successfully" -Level SUCCESS
    }
    catch {
        Write-Log "Invalid configuration file: $($_.Exception.Message)" -Level ERROR
        return $false
    }
    
    # Check test scripts
    $pythonScript = "../k6-tests/python-api-test.js"
    $golangScript = "../k6-tests/golang-api-test.js"
    
    if (-not (Test-Path $pythonScript)) {
        Write-Log "Python API test script not found: $pythonScript" -Level ERROR
        return $false
    }
    
    if (-not (Test-Path $golangScript)) {
        Write-Log "Golang API test script not found: $golangScript" -Level ERROR
        return $false
    }
    
    Write-Log "All prerequisites validated successfully" -Level SUCCESS
    return $true
}

# Create output directories
function Initialize-OutputDirectories {
    $directories = @(
        $OutputDir,
        (Join-Path $OutputDir "raw"),
        (Join-Path $OutputDir "processed"),
        (Join-Path $OutputDir "logs")
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Log "Created directory: $dir" -Level INFO
        }
    }
}

# Perform connectivity tests
function Test-APIConnectivity {
    param($Config)
    
    if ($SkipValidation) {
        Write-Log "Skipping connectivity validation as requested" -Level WARN
        return $true
    }
    
    Write-Log "Testing API connectivity..." -Level INFO
    
    $pythonEndpoint = $Config.apis.python.endpoint
    $golangEndpoint = $Config.apis.golang.endpoint
    
    # Test Python API
    try {
        Write-Log "Testing Python API: $pythonEndpoint" -Level INFO
        $response = Invoke-WebRequest -Uri $pythonEndpoint -Method HEAD -TimeoutSec 10 -UseBasicParsing
        Write-Log "Python API is reachable (Status: $($response.StatusCode))" -Level SUCCESS
    }
    catch {
        Write-Log "Python API connectivity test failed: $($_.Exception.Message)" -Level WARN
    }
    
    # Test Golang API
    try {
        Write-Log "Testing Golang API: $golangEndpoint" -Level INFO
        $response = Invoke-WebRequest -Uri $golangEndpoint -Method HEAD -TimeoutSec 10 -UseBasicParsing
        Write-Log "Golang API is reachable (Status: $($response.StatusCode))" -Level SUCCESS
    }
    catch {
        Write-Log "Golang API connectivity test failed: $($_.Exception.Message)" -Level WARN
    }
    
    return $true
}

# Run k6 test
function Start-K6Test {
    param(
        [string]$TestScript,
        [string]$ApiName,
        [string]$OutputFile,
        [object]$Config
    )
    
    Write-Log "Starting $ApiName API load test..." -Level INFO
    
    $k6Args = @(
        "run",
        "--out", "json=$OutputFile",
        "--summary-trend-stats", "avg,min,med,max,p(90),p(95),p(99)",
        "--summary-time-unit", "ms",
        $TestScript
    )
    
    Write-Log "k6 command: k6 $($k6Args -join ' ')" -Level DEBUG
    
    try {
        $process = Start-Process -FilePath "k6" -ArgumentList $k6Args -NoNewWindow -PassThru -RedirectStandardOutput (Join-Path $OutputDir "logs" "$ApiName-stdout.log") -RedirectStandardError (Join-Path $OutputDir "logs" "$ApiName-stderr.log")
        
        Write-Log "$ApiName test started (PID: $($process.Id))" -Level SUCCESS
        return $process
    }
    catch {
        Write-Log "Failed to start $ApiName test: $($_.Exception.Message)" -Level ERROR
        return $null
    }
}

# Monitor test progress
function Watch-TestProgress {
    param(
        [System.Diagnostics.Process[]]$Processes,
        [string[]]$ApiNames
    )
    
    Write-Log "Monitoring test progress..." -Level INFO
    
    $startTime = Get-Date
    $lastUpdate = $startTime
    
    while ($Processes | Where-Object { -not $_.HasExited }) {
        Start-Sleep -Seconds 5
        
        $currentTime = Get-Date
        $elapsed = $currentTime - $startTime
        
        # Update progress every 30 seconds
        if (($currentTime - $lastUpdate).TotalSeconds -ge 30) {
            Write-Log "Test progress - Elapsed: $($elapsed.ToString('mm\:ss'))" -Level INFO
            
            for ($i = 0; $i -lt $Processes.Length; $i++) {
                $status = if ($Processes[$i].HasExited) { "Completed" } else { "Running" }
                Write-Log "  $($ApiNames[$i]): $status" -Level INFO
            }
            
            $lastUpdate = $currentTime
        }
    }
    
    $totalTime = (Get-Date) - $startTime
    Write-Log "All tests completed in $($totalTime.ToString('mm\:ss'))" -Level SUCCESS
    
    # Check exit codes
    for ($i = 0; $i -lt $Processes.Length; $i++) {
        if ($Processes[$i].ExitCode -eq 0) {
            Write-Log "$($ApiNames[$i]) test completed successfully" -Level SUCCESS
        } else {
            Write-Log "$($ApiNames[$i]) test failed with exit code: $($Processes[$i].ExitCode)" -Level ERROR
        }
    }
}

# Main execution
function Start-LoadTests {
    Write-Log "Starting blur detection API load tests..." -Level INFO
    Write-Log "Configuration: $ConfigPath" -Level INFO
    Write-Log "Output directory: $OutputDir" -Level INFO
    Write-Log "Parallel execution: $Parallel" -Level INFO
    
    # Validate prerequisites
    if (-not (Test-Prerequisites)) {
        Write-Log "Prerequisites validation failed" -Level ERROR
        exit 1
    }
    
    # Initialize output directories
    Initialize-OutputDirectories
    
    # Load configuration
    $config = Get-Content $ConfigPath -Raw | ConvertFrom-Json
    
    # Test API connectivity
    Test-APIConnectivity -Config $config
    
    # Generate unique test run ID
    $testRunId = Get-Date -Format "yyyyMMdd-HHmmss"
    Write-Log "Test run ID: $testRunId" -Level INFO
    
    # Prepare output files
    $pythonOutputFile = Join-Path $OutputDir "raw" "python-api-$testRunId.json"
    $golangOutputFile = Join-Path $OutputDir "raw" "golang-api-$testRunId.json"
    
    # Start tests
    if ($Parallel) {
        Write-Log "Starting tests in parallel..." -Level INFO
        
        $pythonProcess = Start-K6Test -TestScript "../k6-tests/python-api-test.js" -ApiName "Python" -OutputFile $pythonOutputFile -Config $config
        $golangProcess = Start-K6Test -TestScript "../k6-tests/golang-api-test.js" -ApiName "Golang" -OutputFile $golangOutputFile -Config $config
        
        if ($pythonProcess -and $golangProcess) {
            Watch-TestProgress -Processes @($pythonProcess, $golangProcess) -ApiNames @("Python", "Golang")
        } else {
            Write-Log "Failed to start one or more tests" -Level ERROR
            exit 1
        }
    } else {
        Write-Log "Starting tests sequentially..." -Level INFO
        
        # Run Python API test first
        $pythonProcess = Start-K6Test -TestScript "../k6-tests/python-api-test.js" -ApiName "Python" -OutputFile $pythonOutputFile -Config $config
        if ($pythonProcess) {
            Watch-TestProgress -Processes @($pythonProcess) -ApiNames @("Python")
        }
        
        # Run Golang API test second
        $golangProcess = Start-K6Test -TestScript "../k6-tests/golang-api-test.js" -ApiName "Golang" -OutputFile $golangOutputFile -Config $config
        if ($golangProcess) {
            Watch-TestProgress -Processes @($golangProcess) -ApiNames @("Golang")
        }
    }
    
    Write-Log "Load tests execution completed!" -Level SUCCESS
    Write-Log "Results saved to: $OutputDir" -Level INFO
    Write-Log "Next steps:" -Level INFO
    Write-Log "  1. Run analyze-results.ps1 to process the raw data" -Level INFO
    Write-Log "  2. Run generate-dashboard.ps1 to create visualization dashboards" -Level INFO
}

# Execute main function
try {
    Start-LoadTests
}
catch {
    Write-Log "Unexpected error: $($_.Exception.Message)" -Level ERROR
    Write-Log "Stack trace: $($_.ScriptStackTrace)" -Level DEBUG
    exit 1
}
