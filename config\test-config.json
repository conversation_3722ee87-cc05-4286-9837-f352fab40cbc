{"test_parameters": {"duration": "5m", "ramp_up_time": "30s", "virtual_users_per_api": 100, "request_timeout": "30s", "think_time": "1s", "max_retries": 3, "retry_backoff_base": 2, "retry_backoff_max": "10s"}, "apis": {"python": {"name": "Python Blur Detection API", "endpoint": "https://blur.lemoncoast-53375c73.centralindia.azurecontainerapps.io/detect-blur", "method": "POST", "headers": {"Content-Type": "application/json"}, "request_body_template": {"image_url": "{{IMAGE_URL}}"}}, "golang": {"name": "Golang Image Analysis API", "endpoint": "http://fai-imagedescribe.fieldassist.io:8080/analyze", "method": "POST", "headers": {"Content-Type": "application/json"}, "request_body_template": {"url": "{{IMAGE_URL}}", "is_ocr": false}}}, "logging": {"level": "INFO", "max_log_size_mb": 50, "max_log_files": 5, "log_rotation": true}, "output": {"raw_format": "json", "include_response_body": true, "max_response_body_size": 1024, "export_csv": true, "export_summary": true}, "validation": {"pre_test_connectivity_check": true, "validate_image_urls": true, "min_success_rate_threshold": 0.8, "max_error_rate_threshold": 0.2}, "dashboard": {"auto_refresh_interval": 30, "chart_animation": true, "export_charts": true, "responsive_design": true}}