import { check, sleep } from 'k6';
import { Rate, Counter, Trend } from 'k6/metrics';
import http from 'k6/http';

// Custom metrics
export const errorRate = new Rate('errors');
export const successRate = new Rate('success');
export const retryCounter = new Counter('retries');
export const responseBodySize = new Trend('response_body_size');
export const customResponseTime = new Trend('custom_response_time');

// Utility functions
export function validateImageUrl(url) {
    if (!url || typeof url !== 'string') {
        return false;
    }
    
    const urlPattern = /^https?:\/\/.+\.(jpg|jpeg|png|gif|bmp|webp)(\?.*)?$/i;
    const unsplashPattern = /^https:\/\/images\.unsplash\.com\/.+/i;
    
    return urlPattern.test(url) || unsplashPattern.test(url);
}

export function sanitizeResponseBody(body, maxSize = 1024) {
    if (!body) return '';
    
    const bodyStr = typeof body === 'string' ? body : JSON.stringify(body);
    return bodyStr.length > maxSize ? bodyStr.substring(0, maxSize) + '...' : bodyStr;
}

export function createRequestPayload(apiType, imageUrl) {
    switch (apiType) {
        case 'python':
            return JSON.stringify({
                image_url: imageUrl
            });
        case 'golang':
            return JSON.stringify({
                url: imageUrl,
                is_ocr: false
            });
        default:
            throw new Error(`Unknown API type: ${apiType}`);
    }
}

export function validateResponse(response, apiType) {
    const checks = {
        'status is 200': (r) => r.status === 200,
        'response time < 30s': (r) => r.timings.duration < 30000,
        'response has body': (r) => r.body && r.body.length > 0,
        'response is valid JSON': (r) => {
            try {
                JSON.parse(r.body);
                return true;
            } catch (e) {
                return false;
            }
        }
    };

    // API-specific validations
    if (apiType === 'python') {
        checks['python response has expected fields'] = (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.hasOwnProperty('blur_detected') || body.hasOwnProperty('error');
            } catch (e) {
                return false;
            }
        };
    } else if (apiType === 'golang') {
        checks['golang response has expected fields'] = (r) => {
            try {
                const body = JSON.parse(r.body);
                return body.hasOwnProperty('analysis') || body.hasOwnProperty('error');
            } catch (e) {
                return false;
            }
        };
    }

    return check(response, checks);
}

export function exponentialBackoff(attempt, baseDelay = 1000, maxDelay = 10000) {
    const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
    const jitter = Math.random() * 0.1 * delay; // Add 10% jitter
    return delay + jitter;
}

export function makeRequestWithRetry(url, params, apiType, maxRetries = 3) {
    let lastResponse;
    let attempt = 0;
    
    while (attempt <= maxRetries) {
        const startTime = Date.now();
        
        try {
            lastResponse = http.post(url, params.body, {
                headers: params.headers,
                timeout: params.timeout || '30s',
                tags: {
                    api: apiType,
                    attempt: attempt.toString(),
                    image_url: extractImageUrl(params.body, apiType)
                }
            });
            
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            // Record custom metrics
            customResponseTime.add(responseTime);
            responseBodySize.add(lastResponse.body ? lastResponse.body.length : 0);
            
            // Check if request was successful
            if (lastResponse.status === 200) {
                successRate.add(1);
                errorRate.add(0);
                
                if (attempt > 0) {
                    retryCounter.add(attempt);
                }
                
                return lastResponse;
            } else if (lastResponse.status >= 500 && attempt < maxRetries) {
                // Server error, retry
                attempt++;
                const backoffDelay = exponentialBackoff(attempt - 1);
                console.log(`Attempt ${attempt} failed with status ${lastResponse.status}, retrying in ${backoffDelay}ms`);
                sleep(backoffDelay / 1000);
                continue;
            } else {
                // Client error or max retries reached
                break;
            }
        } catch (error) {
            console.error(`Request attempt ${attempt + 1} failed:`, error.message);
            if (attempt < maxRetries) {
                attempt++;
                const backoffDelay = exponentialBackoff(attempt - 1);
                sleep(backoffDelay / 1000);
                continue;
            } else {
                break;
            }
        }
    }
    
    // Record failure metrics
    successRate.add(0);
    errorRate.add(1);
    
    if (attempt > 0) {
        retryCounter.add(attempt);
    }
    
    return lastResponse;
}

function extractImageUrl(requestBody, apiType) {
    try {
        const body = JSON.parse(requestBody);
        return apiType === 'python' ? body.image_url : body.url;
    } catch (e) {
        return 'unknown';
    }
}

export function logTestResult(response, apiType, imageUrl, vuId, iteration) {
    const timestamp = new Date().toISOString();
    const result = {
        timestamp,
        api: apiType,
        image_url: imageUrl,
        vu_id: vuId,
        iteration,
        status_code: response.status,
        response_time_ms: response.timings.duration,
        response_body: sanitizeResponseBody(response.body),
        error: response.status !== 200 ? `HTTP ${response.status}` : null,
        retry_count: response.request ? response.request.tags?.attempt || '0' : '0'
    };
    
    // In k6, we can't directly write to files, but we can log structured data
    // that can be captured by the k6 output
    console.log(`TEST_RESULT: ${JSON.stringify(result)}`);
    
    return result;
}
